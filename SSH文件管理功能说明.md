# SSH文件管理功能说明

## 🎉 功能概述

已成功为电玩管家管理系统添加了完整的**SSH文件管理**功能！现在您可以通过SSH连接到服务器，直接管理服务器上的文件。

## 🔧 主要功能

### 1. SSH连接管理
- **安全连接**: 使用paramiko库建立安全的SSH连接
- **连接测试**: 在连接前可以测试连接是否正常
- **状态显示**: 实时显示连接状态
- **自动断开**: 支持手动断开连接

### 2. 文件浏览
- **实时列表**: 显示服务器上的文件和文件夹
- **详细信息**: 显示文件大小、修改时间、类型
- **路径导航**: 支持快速切换目录
- **自动刷新**: 操作后自动刷新文件列表

### 3. 文件上传
- **选择上传**: 通过文件对话框选择本地文件
- **进度显示**: 实时显示上传进度
- **状态反馈**: 上传成功/失败提示
- **路径确认**: 上传前确认目标路径

### 4. 文件下载
- **选择下载**: 从文件列表选择要下载的文件
- **保存位置**: 自定义本地保存位置
- **进度跟踪**: 实时显示下载进度
- **完成提示**: 下载完成后的状态提示

### 5. 快速导航
- **自选网站更新**: 一键切换到 `/www/wwwroot/zixuan`
- **自选游戏**: 一键切换到 `/www/wwwroot/zixuan/submissions`
- **路径显示**: 当前路径实时显示

## 🖥️ 界面布局

```
文件管理页面
├── SSH服务器连接
│   ├── 连接状态显示
│   ├── [连接服务器] 按钮
│   └── [断开连接] 按钮
├── 路径导航
│   ├── 当前路径显示
│   ├── [自选网站更新] 按钮
│   └── [自选游戏] 按钮
├── 文件操作
│   ├── [刷新文件列表] 按钮
│   ├── [上传文件] 按钮
│   └── [下载选中文件] 按钮
├── 文件列表表格
│   ├── 文件名列
│   ├── 类型列
│   ├── 大小列
│   └── 修改时间列
└── 传输进度
    ├── 进度条
    └── 状态信息
```

## 📋 使用步骤

### 第一步：连接SSH服务器
1. 点击"连接服务器"按钮
2. 填写服务器信息：
   - 服务器地址（IP或域名）
   - 端口（默认22）
   - 用户名
   - 密码
3. 可选择"测试连接"验证信息
4. 点击"连接"建立SSH连接

### 第二步：浏览文件
1. 连接成功后自动显示文件列表
2. 使用"自选网站更新"和"自选游戏"按钮快速切换目录
3. 点击"刷新文件列表"更新显示

### 第三步：上传文件
1. 点击"上传文件"按钮
2. 选择要上传的本地文件
3. 确认上传路径
4. 等待上传完成

### 第四步：下载文件
1. 在文件列表中选择要下载的文件
2. 点击"下载选中文件"按钮
3. 选择本地保存位置
4. 等待下载完成

## 🔒 安全特性

- **加密传输**: 所有数据通过SSH加密传输
- **身份验证**: 需要有效的用户名和密码
- **连接超时**: 防止长时间无响应连接
- **错误处理**: 完善的异常处理机制

## 📦 依赖要求

### 必需库
- **paramiko**: SSH连接库
  ```bash
  pip install paramiko
  ```

### 系统要求
- Python 3.7+
- PyQt6
- 网络连接
- SSH服务器访问权限

## 🚀 技术实现

### 新增类
1. **SSHConnectionDialog**: SSH连接对话框
2. **SSHFileTransferThread**: 文件传输线程

### 新增方法
1. `open_ssh_connection_dialog()` - 打开SSH连接对话框
2. `connect_to_ssh()` - 建立SSH连接
3. `disconnect_ssh()` - 断开SSH连接
4. `change_remote_path()` - 切换远程路径
5. `refresh_remote_files()` - 刷新远程文件列表
6. `upload_file()` - 上传文件
7. `download_selected_file()` - 下载文件
8. `start_file_transfer()` - 开始文件传输

### 核心特性
- **多线程传输**: 文件传输在独立线程中进行，不阻塞UI
- **进度回调**: 实时显示传输进度
- **错误恢复**: 传输失败时的错误处理
- **状态管理**: 完整的连接和传输状态管理

## 🎯 使用场景

### 网站管理
- 上传网站文件到 `/www/wwwroot/zixuan`
- 下载网站备份文件
- 更新网站配置文件

### 游戏管理
- 上传游戏文件到 `/www/wwwroot/zixuan/submissions`
- 下载用户提交的游戏
- 管理游戏资源文件

### 日常维护
- 查看服务器文件状态
- 备份重要文件
- 清理临时文件

## 🔧 故障排除

### 连接问题
- 检查服务器地址和端口
- 验证用户名和密码
- 确认SSH服务是否开启
- 检查防火墙设置

### 传输问题
- 检查网络连接稳定性
- 确认文件权限
- 验证磁盘空间
- 检查文件路径

### 权限问题
- 确认SSH用户有目录访问权限
- 检查文件读写权限
- 验证目录存在性

## 📈 未来扩展

可以考虑添加的功能：
- 文件编辑功能
- 批量文件操作
- 文件搜索功能
- 目录同步功能
- 文件权限管理
- 传输队列管理

## ✅ 完成确认

**🎊 SSH文件管理功能已完全实现！**

- ✅ SSH连接管理
- ✅ 文件浏览功能
- ✅ 文件上传功能
- ✅ 文件下载功能
- ✅ 路径快速切换
- ✅ 进度显示
- ✅ 错误处理
- ✅ 安全传输

现在您可以通过SSH直接管理服务器上的文件，无需手动登录服务器！
