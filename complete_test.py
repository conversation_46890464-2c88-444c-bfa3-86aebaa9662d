#!/usr/bin/env python3
"""
完整的文件管理功能测试
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QMessageBox, QTextEdit)
from PyQt6.QtCore import Qt

class CompleteFileManagementTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("完整文件管理功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("SSH文件管理功能完整测试")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 测试步骤说明
        steps_text = """
测试步骤：

1. 点击"启动主程序"按钮
2. 在主程序中点击左侧"文件管理"
3. 观察按钮状态：
   - 上传文件按钮应该是禁用状态（灰色）
   - 下载文件按钮应该是禁用状态（灰色）
   - 刷新按钮应该是禁用状态（灰色）
4. 点击"连接服务器"按钮
5. 填写SSH连接信息并连接
6. 连接成功后观察按钮状态：
   - 上传文件按钮应该变为启用状态
   - 刷新按钮应该变为启用状态
   - 下载按钮在选择文件后启用
7. 测试上传和下载功能

如果按钮无法点击，请检查：
- 是否已成功连接SSH服务器
- 按钮是否处于启用状态
- 控制台是否有错误信息
        """
        
        steps_label = QLabel(steps_text)
        steps_label.setStyleSheet("background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; font-family: monospace; font-size: 12px;")
        layout.addWidget(steps_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        start_btn = QPushButton("启动主程序")
        start_btn.clicked.connect(self.start_main_app)
        start_btn.setStyleSheet("font-size: 16px; padding: 10px 20px; background: #007bff; color: white;")
        button_layout.addWidget(start_btn)
        
        debug_btn = QPushButton("显示调试信息")
        debug_btn.clicked.connect(self.show_debug_info)
        debug_btn.setStyleSheet("font-size: 16px; padding: 10px 20px;")
        button_layout.addWidget(debug_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("准备开始测试")
        self.status_label.setStyleSheet("background: #f0f0f0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setPlaceholderText("测试日志将显示在这里...")
        layout.addWidget(self.log_text)
        
        self.main_window = None
    
    def log(self, message):
        """添加日志信息"""
        self.log_text.append(f"[LOG] {message}")
        print(f"DEBUG: {message}")
    
    def start_main_app(self):
        """启动主程序"""
        try:
            self.log("正在启动主程序...")
            
            from app import MainWindow
            self.main_window = MainWindow()
            
            self.log("主程序创建成功")
            
            # 切换到文件管理页面
            self.main_window.switch_page("file_management")
            self.log("已切换到文件管理页面")
            
            # 显示主程序
            self.main_window.show()
            self.log("主程序窗口已显示")
            
            # 检查按钮状态
            self.check_button_states()
            
            self.status_label.setText("✅ 主程序已启动，请按照步骤测试")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px; font-weight: bold;")
            
        except Exception as e:
            error_msg = f"主程序启动失败: {str(e)}"
            self.log(error_msg)
            self.status_label.setText(f"❌ {error_msg}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px; font-weight: bold;")
            QMessageBox.warning(self, "错误", error_msg)
    
    def check_button_states(self):
        """检查按钮状态"""
        if not self.main_window:
            return
        
        try:
            # 检查SSH连接状态
            ssh_connected = self.main_window.sftp_client is not None
            self.log(f"SSH连接状态: {'已连接' if ssh_connected else '未连接'}")
            
            # 检查按钮状态
            if hasattr(self.main_window, 'upload_btn'):
                upload_enabled = self.main_window.upload_btn.isEnabled()
                self.log(f"上传按钮状态: {'启用' if upload_enabled else '禁用'}")
            
            if hasattr(self.main_window, 'download_btn'):
                download_enabled = self.main_window.download_btn.isEnabled()
                self.log(f"下载按钮状态: {'启用' if download_enabled else '禁用'}")
            
            if hasattr(self.main_window, 'refresh_btn'):
                refresh_enabled = self.main_window.refresh_btn.isEnabled()
                self.log(f"刷新按钮状态: {'启用' if refresh_enabled else '禁用'}")
            
            if hasattr(self.main_window, 'connect_btn'):
                connect_enabled = self.main_window.connect_btn.isEnabled()
                self.log(f"连接按钮状态: {'启用' if connect_enabled else '禁用'}")
            
            # 检查按钮点击事件是否正确连接
            if hasattr(self.main_window, 'upload_btn'):
                # 检查信号连接
                upload_btn = self.main_window.upload_btn
                receivers = upload_btn.receivers(upload_btn.clicked)
                self.log(f"上传按钮信号连接数: {receivers}")
            
            if hasattr(self.main_window, 'download_btn'):
                download_btn = self.main_window.download_btn
                receivers = download_btn.receivers(download_btn.clicked)
                self.log(f"下载按钮信号连接数: {receivers}")
                
        except Exception as e:
            self.log(f"检查按钮状态时出错: {str(e)}")
    
    def show_debug_info(self):
        """显示调试信息"""
        if not self.main_window:
            QMessageBox.warning(self, "提示", "请先启动主程序")
            return
        
        self.log("=== 调试信息 ===")
        self.check_button_states()
        
        # 显示详细的调试信息
        debug_info = f"""
调试信息：

SSH连接状态: {'已连接' if self.main_window.sftp_client else '未连接'}
当前远程路径: {getattr(self.main_window, 'current_remote_path', 'N/A')}

按钮状态：
- 上传按钮: {'启用' if hasattr(self.main_window, 'upload_btn') and self.main_window.upload_btn.isEnabled() else '禁用'}
- 下载按钮: {'启用' if hasattr(self.main_window, 'download_btn') and self.main_window.download_btn.isEnabled() else '禁用'}
- 刷新按钮: {'启用' if hasattr(self.main_window, 'refresh_btn') and self.main_window.refresh_btn.isEnabled() else '禁用'}
- 连接按钮: {'启用' if hasattr(self.main_window, 'connect_btn') and self.main_window.connect_btn.isEnabled() else '禁用'}

解决方案：
1. 如果按钮都是禁用状态，请先点击"连接服务器"
2. 填写正确的SSH连接信息
3. 连接成功后按钮会自动启用
4. 如果连接后仍无法点击，请查看控制台错误信息
        """
        
        QMessageBox.information(self, "调试信息", debug_info)

def main():
    app = QApplication(sys.argv)
    window = CompleteFileManagementTest()
    window.show()
    
    print("DEBUG: 完整测试程序已启动")
    print("DEBUG: 请按照界面提示进行测试")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
