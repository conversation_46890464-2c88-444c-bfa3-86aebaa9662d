Metadata-Version: 2.2
Name: configparser
Version: 7.2.0
Summary: Updated configparser from stdlib for earlier Pythons.
Author-email: <PERSON><PERSON><PERSON> <l<PERSON><PERSON>@langa.pl>
Maintainer-email: "<PERSON>" <<EMAIL>>
Project-URL: Source, https://github.com/jaraco/configparser
Keywords: configparser,ini,parsing,conf,cfg,configuration,file
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: test
Requires-Dist: pytest!=8.1.*,>=6; extra == "test"
Requires-Dist: types-backports; extra == "test"
Provides-Extra: doc
Requires-Dist: sphinx>=3.5; extra == "doc"
Requires-Dist: jaraco.packaging>=9.3; extra == "doc"
Requires-Dist: rst.linker>=1.9; extra == "doc"
Requires-Dist: furo; extra == "doc"
Requires-Dist: sphinx-lint; extra == "doc"
Requires-Dist: jaraco.tidelift>=1.4; extra == "doc"
Provides-Extra: check
Requires-Dist: pytest-checkdocs>=2.4; extra == "check"
Requires-Dist: pytest-ruff>=0.2.1; sys_platform != "cygwin" and extra == "check"
Provides-Extra: cover
Requires-Dist: pytest-cov; extra == "cover"
Provides-Extra: enabler
Requires-Dist: pytest-enabler>=2.2; extra == "enabler"
Provides-Extra: type
Requires-Dist: pytest-mypy; extra == "type"

.. image:: https://img.shields.io/pypi/v/configparser.svg
   :target: https://pypi.org/project/configparser

.. image:: https://img.shields.io/pypi/pyversions/configparser.svg

.. image:: https://github.com/jaraco/configparser/actions/workflows/main.yml/badge.svg
   :target: https://github.com/jaraco/configparser/actions?query=workflow%3A%22tests%22
   :alt: tests

.. image:: https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json
    :target: https://github.com/astral-sh/ruff
    :alt: Ruff

.. image:: https://readthedocs.org/projects/configparser/badge/?version=latest
   :target: https://configparser.readthedocs.io/en/latest/?badge=latest

.. image:: https://img.shields.io/badge/skeleton-2025-informational
   :target: https://blog.jaraco.com/skeleton

.. image:: https://tidelift.com/badges/package/pypi/configparser
   :target: https://tidelift.com/subscription/pkg/pypi-configparser?utm_source=pypi-configparser&utm_medium=readme


This package is a backport of the refreshed and enhanced ConfigParser from
later Python versions. To use the backport instead of the built-in version,
simply import it explicitly as a backport::

  from backports import configparser

For detailed documentation consult the vanilla version at
http://docs.python.org/3/library/configparser.html.


Versioning
==========

This project uses `semver <https://semver.org/spec/v2.0.0.html>`_ to
communicate the impact of various releases while periodically syncing
with the upstream implementation in CPython.
The `history <https://configparser.readthedocs.io/en/latest/history.html>`_
serves as a reference indicating which versions incorporate
which upstream functionality.

Prior to the ``4.0.0`` release, `another scheme
<https://github.com/jaraco/configparser/blob/3.8.1/README.rst#versioning>`_
was used to associate the CPython and backports releases.

Maintenance
===========

This backport was originally authored by Łukasz Langa, the current vanilla
``configparser`` maintainer for CPython and is currently maintained by
Jason R. Coombs:

* `configparser repository <https://github.com/jaraco/configparser>`_

* `configparser issue tracker <https://github.com/jaraco/configparser/issues>`_

Conversion Process
==================

This section is technical and should bother you only if you are wondering how
this backport is produced. If the implementation details of this backport are
not important for you, feel free to ignore the following content.

The project takes the following branching approach:

* The ``cpython`` branch holds unchanged files synchronized from the upstream
  CPython repository. The synchronization is currently done by manually copying
  the required files and stating from which CPython changeset they come.

* The ``main`` branch holds a version of the ``cpython`` code with some tweaks
  that make it compatible with older Pythons. Code on this branch must work
  on all supported Python versions. Test with ``tox`` or in CI.

The process works like this:

1. In the ``cpython`` branch, run ``./sync-upstream``, which
   downloads the latest stable release of Python and copies the relevant
   files from there into their new locations and then commits those
   changes with a nice reference to the relevant upstream commit hash.

2. Merge the new commit to ``main``.

3. Check for new names in ``__all__`` and update imports in
   ``configparser/__init__.py`` accordingly. Run tests. Commit.

4. Make any compatibility changes on ``main``. Run tests. Commit.

5. Update the docs and release the new version.


For Enterprise
==============

Available as part of the Tidelift Subscription.

This project and the maintainers of thousands of other packages are working with Tidelift to deliver one enterprise subscription that covers all of the open source you use.

`Learn more <https://tidelift.com/subscription/pkg/pypi-configparser?utm_source=pypi-configparser&utm_medium=referral&utm_campaign=github>`_.
