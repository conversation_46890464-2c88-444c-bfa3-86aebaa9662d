# The PEP 484 type hints stub file for the QtQuick module.
#
# Generated by SIP 6.12.0
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import collections, re, typing, enum

try:
    from warnings import deprecated
except ImportError:
    pass

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui
from PyQt6 import QtNetwork
from PyQt6 import QtQml

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[collections.abc.Callable[..., Any], QtCore.pyqtBoundSignal]


class QQuickItem(QtCore.QObject, QtQml.QQmlParserStatus):

    class TransformOrigin(enum.Enum):
        TopLeft = ... # type: QQuickItem.TransformOrigin
        Top = ... # type: QQuickItem.TransformOrigin
        TopRight = ... # type: QQuickItem.TransformOrigin
        Left = ... # type: QQuickItem.TransformOrigin
        Center = ... # type: QQuickItem.TransformOrigin
        Right = ... # type: QQuickItem.TransformOrigin
        BottomLeft = ... # type: QQuickItem.TransformOrigin
        Bottom = ... # type: QQuickItem.TransformOrigin
        BottomRight = ... # type: QQuickItem.TransformOrigin

    class ItemChange(enum.Enum):
        ItemChildAddedChange = ... # type: QQuickItem.ItemChange
        ItemChildRemovedChange = ... # type: QQuickItem.ItemChange
        ItemSceneChange = ... # type: QQuickItem.ItemChange
        ItemVisibleHasChanged = ... # type: QQuickItem.ItemChange
        ItemParentHasChanged = ... # type: QQuickItem.ItemChange
        ItemOpacityHasChanged = ... # type: QQuickItem.ItemChange
        ItemActiveFocusHasChanged = ... # type: QQuickItem.ItemChange
        ItemRotationHasChanged = ... # type: QQuickItem.ItemChange
        ItemAntialiasingHasChanged = ... # type: QQuickItem.ItemChange
        ItemDevicePixelRatioHasChanged = ... # type: QQuickItem.ItemChange
        ItemEnabledHasChanged = ... # type: QQuickItem.ItemChange
        ItemScaleHasChanged = ... # type: QQuickItem.ItemChange
        ItemTransformHasChanged = ... # type: QQuickItem.ItemChange

    class Flag(enum.Flag):
        ItemClipsChildrenToShape = ... # type: QQuickItem.Flag
        ItemAcceptsInputMethod = ... # type: QQuickItem.Flag
        ItemIsFocusScope = ... # type: QQuickItem.Flag
        ItemHasContents = ... # type: QQuickItem.Flag
        ItemAcceptsDrops = ... # type: QQuickItem.Flag
        ItemIsViewport = ... # type: QQuickItem.Flag
        ItemObservesViewport = ... # type: QQuickItem.Flag

    class ItemChangeData(PyQt6.sip.simplewrapper):

        boolValue = ... # type: bool
        item = ... # type: 'QQuickItem'
        realValue = ... # type: float
        window = ... # type: 'QQuickWindow'

        @typing.overload
        def __init__(self, v: typing.Optional['QQuickItem']) -> None: ...
        @typing.overload
        def __init__(self, v: typing.Optional['QQuickWindow']) -> None: ...
        @typing.overload
        def __init__(self, v: float) -> None: ...
        @typing.overload
        def __init__(self, v: bool) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QQuickItem.ItemChangeData') -> None: ...

    class UpdatePaintNodeData(PyQt6.sip.simplewrapper):

        transformNode = ... # type: 'QSGTransformNode'

        def __init__(self, a0: 'QQuickItem.UpdatePaintNodeData') -> None: ...

    def __init__(self, parent: typing.Optional['QQuickItem'] = ...) -> None: ...

    focusPolicyChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setFocusPolicy(self, policy: QtCore.Qt.FocusPolicy) -> None: ...
    def focusPolicy(self) -> QtCore.Qt.FocusPolicy: ...
    def dumpItemTree(self) -> None: ...
    def ensurePolished(self) -> None: ...
    def viewportItem(self) -> typing.Optional['QQuickItem']: ...
    containmentMaskChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setContainmentMask(self, mask: typing.Optional[QtCore.QObject]) -> None: ...
    def containmentMask(self) -> typing.Optional[QtCore.QObject]: ...
    def setAcceptTouchEvents(self, accept: bool) -> None: ...
    def acceptTouchEvents(self) -> bool: ...
    def size(self) -> QtCore.QSizeF: ...
    def mapFromGlobal(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    def mapToGlobal(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    def isAncestorOf(self, child: typing.Optional['QQuickItem']) -> bool: ...
    def grabToImage(self, targetSize: QtCore.QSize = ...) -> typing.Optional['QQuickItemGrabResult']: ...
    def resetAntialiasing(self) -> None: ...
    windowChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeFocusOnTabChanged: typing.ClassVar[QtCore.pyqtSignal]
    def nextItemInFocusChain(self, forward: bool = ...) -> typing.Optional['QQuickItem']: ...
    def setActiveFocusOnTab(self, a0: bool) -> None: ...
    def activeFocusOnTab(self) -> bool: ...
    def updatePolish(self) -> None: ...
    def releaseResources(self) -> None: ...
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional['QQuickItem.UpdatePaintNodeData']) -> typing.Optional['QSGNode']: ...
    def geometryChange(self, newGeometry: QtCore.QRectF, oldGeometry: QtCore.QRectF) -> None: ...
    def childMouseEventFilter(self, a0: typing.Optional['QQuickItem'], a1: typing.Optional[QtCore.QEvent]) -> bool: ...
    def dropEvent(self, a0: typing.Optional[QtGui.QDropEvent]) -> None: ...
    def dragLeaveEvent(self, a0: typing.Optional[QtGui.QDragLeaveEvent]) -> None: ...
    def dragMoveEvent(self, a0: typing.Optional[QtGui.QDragMoveEvent]) -> None: ...
    def dragEnterEvent(self, a0: typing.Optional[QtGui.QDragEnterEvent]) -> None: ...
    def hoverLeaveEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def hoverMoveEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def hoverEnterEvent(self, event: typing.Optional[QtGui.QHoverEvent]) -> None: ...
    def touchEvent(self, event: typing.Optional[QtGui.QTouchEvent]) -> None: ...
    def wheelEvent(self, event: typing.Optional[QtGui.QWheelEvent]) -> None: ...
    def touchUngrabEvent(self) -> None: ...
    def mouseUngrabEvent(self) -> None: ...
    def mouseDoubleClickEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseMoveEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, event: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def focusOutEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def focusInEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def inputMethodEvent(self, a0: typing.Optional[QtGui.QInputMethodEvent]) -> None: ...
    def keyReleaseEvent(self, event: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, event: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def componentComplete(self) -> None: ...
    def classBegin(self) -> None: ...
    def heightValid(self) -> bool: ...
    def widthValid(self) -> bool: ...
    def updateInputMethod(self, queries: QtCore.Qt.InputMethodQuery = ...) -> None: ...
    def itemChange(self, a0: 'QQuickItem.ItemChange', a1: 'QQuickItem.ItemChangeData') -> None: ...
    def isComponentComplete(self) -> bool: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    implicitHeightChanged: typing.ClassVar[QtCore.pyqtSignal]
    implicitWidthChanged: typing.ClassVar[QtCore.pyqtSignal]
    zChanged: typing.ClassVar[QtCore.pyqtSignal]
    heightChanged: typing.ClassVar[QtCore.pyqtSignal]
    widthChanged: typing.ClassVar[QtCore.pyqtSignal]
    yChanged: typing.ClassVar[QtCore.pyqtSignal]
    xChanged: typing.ClassVar[QtCore.pyqtSignal]
    scaleChanged: typing.ClassVar[QtCore.pyqtSignal]
    rotationChanged: typing.ClassVar[QtCore.pyqtSignal]
    visibleChanged: typing.ClassVar[QtCore.pyqtSignal]
    enabledChanged: typing.ClassVar[QtCore.pyqtSignal]
    opacityChanged: typing.ClassVar[QtCore.pyqtSignal]
    clipChanged: typing.ClassVar[QtCore.pyqtSignal]
    antialiasingChanged: typing.ClassVar[QtCore.pyqtSignal]
    smoothChanged: typing.ClassVar[QtCore.pyqtSignal]
    transformOriginChanged: typing.ClassVar[QtCore.pyqtSignal]
    parentChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeFocusChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusChanged: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    baselineOffsetChanged: typing.ClassVar[QtCore.pyqtSignal]
    childrenRectChanged: typing.ClassVar[QtCore.pyqtSignal]
    def update(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    def inputMethodQuery(self, query: QtCore.Qt.InputMethodQuery) -> typing.Any: ...
    def childAt(self, x: float, y: float) -> typing.Optional['QQuickItem']: ...
    @typing.overload
    def forceActiveFocus(self) -> None: ...
    @typing.overload
    def forceActiveFocus(self, reason: QtCore.Qt.FocusReason) -> None: ...
    def polish(self) -> None: ...
    def mapRectFromScene(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapRectFromItem(self, item: typing.Optional['QQuickItem'], rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapFromScene(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    def mapFromItem(self, item: typing.Optional['QQuickItem'], point: QtCore.QPointF) -> QtCore.QPointF: ...
    def mapRectToScene(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapRectToItem(self, item: typing.Optional['QQuickItem'], rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def mapToScene(self, point: QtCore.QPointF) -> QtCore.QPointF: ...
    def mapToItem(self, item: typing.Optional['QQuickItem'], point: QtCore.QPointF) -> QtCore.QPointF: ...
    def contains(self, point: QtCore.QPointF) -> bool: ...
    def setKeepTouchGrab(self, a0: bool) -> None: ...
    def keepTouchGrab(self) -> bool: ...
    def ungrabTouchPoints(self) -> None: ...
    def grabTouchPoints(self, ids: collections.abc.Iterable[int]) -> None: ...
    def setFiltersChildMouseEvents(self, filter: bool) -> None: ...
    def filtersChildMouseEvents(self) -> bool: ...
    def setKeepMouseGrab(self, a0: bool) -> None: ...
    def keepMouseGrab(self) -> bool: ...
    def ungrabMouse(self) -> None: ...
    def grabMouse(self) -> None: ...
    def unsetCursor(self) -> None: ...
    def setCursor(self, cursor: typing.Union[QtGui.QCursor, QtCore.Qt.CursorShape]) -> None: ...
    def cursor(self) -> QtGui.QCursor: ...
    def setAcceptHoverEvents(self, enabled: bool) -> None: ...
    def acceptHoverEvents(self) -> bool: ...
    def setAcceptedMouseButtons(self, buttons: QtCore.Qt.MouseButton) -> None: ...
    def acceptedMouseButtons(self) -> QtCore.Qt.MouseButton: ...
    def scopedFocusItem(self) -> typing.Optional['QQuickItem']: ...
    def isFocusScope(self) -> bool: ...
    @typing.overload
    def setFocus(self, a0: bool) -> None: ...
    @typing.overload
    def setFocus(self, focus: bool, reason: QtCore.Qt.FocusReason) -> None: ...
    def hasFocus(self) -> bool: ...
    def hasActiveFocus(self) -> bool: ...
    def setFlags(self, flags: 'QQuickItem.Flag') -> None: ...
    def setFlag(self, flag: 'QQuickItem.Flag', enabled: bool = ...) -> None: ...
    def flags(self) -> 'QQuickItem.Flag': ...
    def setAntialiasing(self, a0: bool) -> None: ...
    def antialiasing(self) -> bool: ...
    def setSmooth(self, a0: bool) -> None: ...
    def smooth(self) -> bool: ...
    def setEnabled(self, a0: bool) -> None: ...
    def isEnabled(self) -> bool: ...
    def setVisible(self, a0: bool) -> None: ...
    def isVisible(self) -> bool: ...
    def setOpacity(self, a0: float) -> None: ...
    def opacity(self) -> float: ...
    def setScale(self, a0: float) -> None: ...
    def scale(self) -> float: ...
    def setRotation(self, a0: float) -> None: ...
    def rotation(self) -> float: ...
    def setZ(self, a0: float) -> None: ...
    def z(self) -> float: ...
    def setTransformOrigin(self, a0: 'QQuickItem.TransformOrigin') -> None: ...
    def transformOrigin(self) -> 'QQuickItem.TransformOrigin': ...
    def setSize(self, size: QtCore.QSizeF) -> None: ...
    def implicitHeight(self) -> float: ...
    def setImplicitHeight(self, a0: float) -> None: ...
    def resetHeight(self) -> None: ...
    def setHeight(self, a0: float) -> None: ...
    def height(self) -> float: ...
    def implicitWidth(self) -> float: ...
    def setImplicitWidth(self, a0: float) -> None: ...
    def resetWidth(self) -> None: ...
    def setWidth(self, a0: float) -> None: ...
    def width(self) -> float: ...
    def setY(self, a0: float) -> None: ...
    def setX(self, a0: float) -> None: ...
    def y(self) -> float: ...
    def x(self) -> float: ...
    def setBaselineOffset(self, a0: float) -> None: ...
    def baselineOffset(self) -> float: ...
    def setState(self, a0: typing.Optional[str]) -> None: ...
    def state(self) -> str: ...
    def setClip(self, a0: bool) -> None: ...
    def clip(self) -> bool: ...
    def childItems(self) -> list['QQuickItem']: ...
    def childrenRect(self) -> QtCore.QRectF: ...
    def stackAfter(self, a0: typing.Optional['QQuickItem']) -> None: ...
    def stackBefore(self, a0: typing.Optional['QQuickItem']) -> None: ...
    def setParentItem(self, parent: typing.Optional['QQuickItem']) -> None: ...
    def parentItem(self) -> typing.Optional['QQuickItem']: ...
    def window(self) -> typing.Optional['QQuickWindow']: ...


class QQuickFramebufferObject(QQuickItem):

    class Renderer(PyQt6.sip.wrapper):

        try:
            from PyQt6.QtOpenGL import QOpenGLFramebufferObject
        except ImportError:
            pass

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QQuickFramebufferObject.Renderer') -> None: ...

        def invalidateFramebufferObject(self) -> None: ...
        def update(self) -> None: ...
        def framebufferObject(self) -> typing.Optional[QOpenGLFramebufferObject]: ...
        def synchronize(self, a0: typing.Optional['QQuickFramebufferObject']) -> None: ...
        def createFramebufferObject(self, size: QtCore.QSize) -> typing.Optional[QOpenGLFramebufferObject]: ...
        def render(self) -> None: ...

    def __init__(self, parent: typing.Optional[QQuickItem] = ...) -> None: ...

    mirrorVerticallyChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setMirrorVertically(self, enable: bool) -> None: ...
    def mirrorVertically(self) -> bool: ...
    def releaseResources(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    textureFollowsItemSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional[QQuickItem.UpdatePaintNodeData]) -> typing.Optional['QSGNode']: ...
    def geometryChange(self, newGeometry: QtCore.QRectF, oldGeometry: QtCore.QRectF) -> None: ...
    def createRenderer(self) -> typing.Optional['QQuickFramebufferObject.Renderer']: ...
    def setTextureFollowsItemSize(self, follows: bool) -> None: ...
    def textureFollowsItemSize(self) -> bool: ...


class QQuickGraphicsConfiguration(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QQuickGraphicsConfiguration') -> None: ...

    def timestampsEnabled(self) -> bool: ...
    def setTimestamps(self, enable: bool) -> None: ...
    def pipelineCacheLoadFile(self) -> str: ...
    def setPipelineCacheLoadFile(self, filename: typing.Optional[str]) -> None: ...
    def pipelineCacheSaveFile(self) -> str: ...
    def setPipelineCacheSaveFile(self, filename: typing.Optional[str]) -> None: ...
    def isAutomaticPipelineCacheEnabled(self) -> bool: ...
    def setAutomaticPipelineCache(self, enable: bool) -> None: ...
    def prefersSoftwareDevice(self) -> bool: ...
    def setPreferSoftwareDevice(self, enable: bool) -> None: ...
    def isDebugMarkersEnabled(self) -> bool: ...
    def setDebugMarkers(self, enable: bool) -> None: ...
    def isDebugLayerEnabled(self) -> bool: ...
    def setDebugLayer(self, enable: bool) -> None: ...
    @staticmethod
    def preferredInstanceExtensions() -> list[QtCore.QByteArray]: ...
    def isDepthBufferEnabledFor2D(self) -> bool: ...
    def setDepthBufferFor2D(self, enable: bool) -> None: ...
    def deviceExtensions(self) -> list[QtCore.QByteArray]: ...
    def setDeviceExtensions(self, extensions: collections.abc.Iterable[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]]) -> None: ...


class QQuickGraphicsDevice(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QQuickGraphicsDevice') -> None: ...

    @staticmethod
    def fromOpenGLContext(context: typing.Optional[QtGui.QOpenGLContext]) -> 'QQuickGraphicsDevice': ...
    def isNull(self) -> bool: ...


class QQuickTextureFactory(QtCore.QObject):

    def __init__(self) -> None: ...

    @staticmethod
    def textureFactoryForImage(image: QtGui.QImage) -> typing.Optional['QQuickTextureFactory']: ...
    def image(self) -> QtGui.QImage: ...
    def textureByteCount(self) -> int: ...
    def textureSize(self) -> QtCore.QSize: ...
    def createTexture(self, window: typing.Optional['QQuickWindow']) -> typing.Optional['QSGTexture']: ...


class QQuickImageProvider(QtQml.QQmlImageProviderBase):

    def __init__(self, type: QtQml.QQmlImageProviderBase.ImageType, flags: QtQml.QQmlImageProviderBase.Flag = ...) -> None: ...

    def requestTexture(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[typing.Optional[QQuickTextureFactory], typing.Optional[QtCore.QSize]]: ...
    def requestPixmap(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[QtGui.QPixmap, typing.Optional[QtCore.QSize]]: ...
    def requestImage(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Tuple[QtGui.QImage, typing.Optional[QtCore.QSize]]: ...
    def flags(self) -> QtQml.QQmlImageProviderBase.Flag: ...
    def imageType(self) -> QtQml.QQmlImageProviderBase.ImageType: ...


class QQuickImageResponse(QtCore.QObject):

    def __init__(self) -> None: ...

    finished: typing.ClassVar[QtCore.pyqtSignal]
    def cancel(self) -> None: ...
    def errorString(self) -> str: ...
    def textureFactory(self) -> typing.Optional[QQuickTextureFactory]: ...


class QQuickAsyncImageProvider(QQuickImageProvider):

    def __init__(self) -> None: ...

    def requestImageResponse(self, id: typing.Optional[str], requestedSize: QtCore.QSize) -> typing.Optional[QQuickImageResponse]: ...


class QQuickItemGrabResult(QtCore.QObject):

    ready: typing.ClassVar[QtCore.pyqtSignal]
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    @typing.overload
    def saveToFile(self, fileName: typing.Optional[str]) -> bool: ...
    @typing.overload
    def saveToFile(self, fileName: QtCore.QUrl) -> bool: ...
    def url(self) -> QtCore.QUrl: ...
    def image(self) -> QtGui.QImage: ...


class QQuickPaintedItem(QQuickItem):

    class PerformanceHint(enum.Flag):
        FastFBOResizing = ... # type: QQuickPaintedItem.PerformanceHint

    class RenderTarget(enum.Enum):
        Image = ... # type: QQuickPaintedItem.RenderTarget
        FramebufferObject = ... # type: QQuickPaintedItem.RenderTarget
        InvertedYFramebufferObject = ... # type: QQuickPaintedItem.RenderTarget

    def __init__(self, parent: typing.Optional[QQuickItem] = ...) -> None: ...

    textureSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setTextureSize(self, size: QtCore.QSize) -> None: ...
    def textureSize(self) -> QtCore.QSize: ...
    def itemChange(self, a0: QQuickItem.ItemChange, a1: QQuickItem.ItemChangeData) -> None: ...
    def releaseResources(self) -> None: ...
    def textureProvider(self) -> typing.Optional['QSGTextureProvider']: ...
    def isTextureProvider(self) -> bool: ...
    def updatePaintNode(self, a0: typing.Optional['QSGNode'], a1: typing.Optional[QQuickItem.UpdatePaintNodeData]) -> typing.Optional['QSGNode']: ...
    renderTargetChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsScaleChanged: typing.ClassVar[QtCore.pyqtSignal]
    contentsSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    fillColorChanged: typing.ClassVar[QtCore.pyqtSignal]
    def paint(self, painter: typing.Optional[QtGui.QPainter]) -> None: ...
    def setRenderTarget(self, target: 'QQuickPaintedItem.RenderTarget') -> None: ...
    def renderTarget(self) -> 'QQuickPaintedItem.RenderTarget': ...
    def setFillColor(self, a0: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def fillColor(self) -> QtGui.QColor: ...
    def setContentsScale(self, a0: float) -> None: ...
    def contentsScale(self) -> float: ...
    def resetContentsSize(self) -> None: ...
    def setContentsSize(self, a0: QtCore.QSize) -> None: ...
    def contentsSize(self) -> QtCore.QSize: ...
    def contentsBoundingRect(self) -> QtCore.QRectF: ...
    def setPerformanceHints(self, hints: 'QQuickPaintedItem.PerformanceHint') -> None: ...
    def setPerformanceHint(self, hint: 'QQuickPaintedItem.PerformanceHint', enabled: bool = ...) -> None: ...
    def performanceHints(self) -> 'QQuickPaintedItem.PerformanceHint': ...
    def setMipmap(self, enable: bool) -> None: ...
    def mipmap(self) -> bool: ...
    def setAntialiasing(self, enable: bool) -> None: ...
    def antialiasing(self) -> bool: ...
    def setOpaquePainting(self, opaque: bool) -> None: ...
    def opaquePainting(self) -> bool: ...
    def update(self, rect: QtCore.QRect = ...) -> None: ...


class QQuickRenderControl(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def window(self) -> typing.Optional['QQuickWindow']: ...
    def endFrame(self) -> None: ...
    def beginFrame(self) -> None: ...
    def samples(self) -> int: ...
    def setSamples(self, sampleCount: int) -> None: ...
    sceneChanged: typing.ClassVar[QtCore.pyqtSignal]
    renderRequested: typing.ClassVar[QtCore.pyqtSignal]
    def prepareThread(self, targetThread: typing.Optional[QtCore.QThread]) -> None: ...
    def renderWindow(self, offset: typing.Optional[QtCore.QPoint]) -> typing.Optional[QtGui.QWindow]: ...
    @staticmethod
    def renderWindowFor(win: typing.Optional['QQuickWindow'], offset: typing.Optional[QtCore.QPoint] = ...) -> typing.Optional[QtGui.QWindow]: ...
    def sync(self) -> bool: ...
    def render(self) -> None: ...
    def polishItems(self) -> None: ...
    def invalidate(self) -> None: ...
    def initialize(self) -> bool: ...


class QQuickRenderTarget(PyQt6.sip.simplewrapper):

    class Flag(enum.Enum):
        MultisampleResolve = ... # type: QQuickRenderTarget.Flag

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QQuickRenderTarget') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def setMirrorVertically(self, enable: bool) -> None: ...
    def mirrorVertically(self) -> bool: ...
    def setDevicePixelRatio(self, ratio: float) -> None: ...
    def devicePixelRatio(self) -> float: ...
    @staticmethod
    def fromPaintDevice(device: typing.Optional[QtGui.QPaintDevice]) -> 'QQuickRenderTarget': ...
    @staticmethod
    def fromOpenGLRenderBuffer(renderbufferId: int, pixelSize: QtCore.QSize, sampleCount: int = ...) -> 'QQuickRenderTarget': ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, pixelSize: QtCore.QSize, sampleCount: int = ...) -> 'QQuickRenderTarget': ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, format: int, pixelSize: QtCore.QSize, sampleCount: int = ...) -> 'QQuickRenderTarget': ...
    @typing.overload
    @staticmethod
    def fromOpenGLTexture(textureId: int, format: int, pixelSize: QtCore.QSize, sampleCount: int, arraySize: int, flags: 'QQuickRenderTarget.Flag') -> 'QQuickRenderTarget': ...
    def isNull(self) -> bool: ...


class QQuickTextDocument(QtCore.QObject):

    class Status(enum.Enum):
        Null = ... # type: QQuickTextDocument.Status
        Loading = ... # type: QQuickTextDocument.Status
        Loaded = ... # type: QQuickTextDocument.Status
        Saving = ... # type: QQuickTextDocument.Status
        Saved = ... # type: QQuickTextDocument.Status
        ReadError = ... # type: QQuickTextDocument.Status
        WriteError = ... # type: QQuickTextDocument.Status
        NonLocalFileError = ... # type: QQuickTextDocument.Status

    def __init__(self, parent: typing.Optional[QQuickItem]) -> None: ...

    errorStringChanged: typing.ClassVar[QtCore.pyqtSignal]
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    modifiedChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    textDocumentChanged: typing.ClassVar[QtCore.pyqtSignal]
    def errorString(self) -> str: ...
    def status(self) -> 'QQuickTextDocument.Status': ...
    def saveAs(self, url: QtCore.QUrl) -> None: ...
    def save(self) -> None: ...
    def setTextDocument(self, document: typing.Optional[QtGui.QTextDocument]) -> None: ...
    def setModified(self, modified: bool) -> None: ...
    def isModified(self) -> bool: ...
    def setSource(self, url: QtCore.QUrl) -> None: ...
    def source(self) -> QtCore.QUrl: ...
    def textDocument(self) -> typing.Optional[QtGui.QTextDocument]: ...


class QQuickWindow(QtGui.QWindow):

    class TextRenderType(enum.Enum):
        QtTextRendering = ... # type: QQuickWindow.TextRenderType
        NativeTextRendering = ... # type: QQuickWindow.TextRenderType
        CurveTextRendering = ... # type: QQuickWindow.TextRenderType

    class RenderStage(enum.Enum):
        BeforeSynchronizingStage = ... # type: QQuickWindow.RenderStage
        AfterSynchronizingStage = ... # type: QQuickWindow.RenderStage
        BeforeRenderingStage = ... # type: QQuickWindow.RenderStage
        AfterRenderingStage = ... # type: QQuickWindow.RenderStage
        AfterSwapStage = ... # type: QQuickWindow.RenderStage
        NoStage = ... # type: QQuickWindow.RenderStage

    class SceneGraphError(enum.Enum):
        ContextNotAvailable = ... # type: QQuickWindow.SceneGraphError

    class CreateTextureOption(enum.Flag):
        TextureHasAlphaChannel = ... # type: QQuickWindow.CreateTextureOption
        TextureHasMipmaps = ... # type: QQuickWindow.CreateTextureOption
        TextureOwnsGLTexture = ... # type: QQuickWindow.CreateTextureOption
        TextureCanUseAtlas = ... # type: QQuickWindow.CreateTextureOption
        TextureIsOpaque = ... # type: QQuickWindow.CreateTextureOption

    def __init__(self, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...

    def graphicsConfiguration(self) -> QQuickGraphicsConfiguration: ...
    def setGraphicsConfiguration(self, config: QQuickGraphicsConfiguration) -> None: ...
    def graphicsDevice(self) -> QQuickGraphicsDevice: ...
    def setGraphicsDevice(self, device: QQuickGraphicsDevice) -> None: ...
    @staticmethod
    def graphicsApi() -> 'QSGRendererInterface.GraphicsApi': ...
    @staticmethod
    def setGraphicsApi(api: 'QSGRendererInterface.GraphicsApi') -> None: ...
    def isPersistentGraphics(self) -> bool: ...
    def setPersistentGraphics(self, persistent: bool) -> None: ...
    afterFrameEnd: typing.ClassVar[QtCore.pyqtSignal]
    beforeFrameBegin: typing.ClassVar[QtCore.pyqtSignal]
    afterRenderPassRecording: typing.ClassVar[QtCore.pyqtSignal]
    beforeRenderPassRecording: typing.ClassVar[QtCore.pyqtSignal]
    def endExternalCommands(self) -> None: ...
    def beginExternalCommands(self) -> None: ...
    @staticmethod
    def setTextRenderType(renderType: 'QQuickWindow.TextRenderType') -> None: ...
    @staticmethod
    def textRenderType() -> 'QQuickWindow.TextRenderType': ...
    @staticmethod
    def sceneGraphBackend() -> str: ...
    def createImageNode(self) -> typing.Optional['QSGImageNode']: ...
    def createRectangleNode(self) -> typing.Optional['QSGRectangleNode']: ...
    @staticmethod
    def setSceneGraphBackend(backend: typing.Optional[str]) -> None: ...
    def rendererInterface(self) -> typing.Optional['QSGRendererInterface']: ...
    def isSceneGraphInitialized(self) -> bool: ...
    def effectiveDevicePixelRatio(self) -> float: ...
    def scheduleRenderJob(self, job: typing.Optional[QtCore.QRunnable], schedule: 'QQuickWindow.RenderStage') -> None: ...
    sceneGraphError: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphAboutToStop: typing.ClassVar[QtCore.pyqtSignal]
    afterAnimating: typing.ClassVar[QtCore.pyqtSignal]
    afterSynchronizing: typing.ClassVar[QtCore.pyqtSignal]
    activeFocusItemChanged: typing.ClassVar[QtCore.pyqtSignal]
    closing: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def setDefaultAlphaBuffer(useAlpha: bool) -> None: ...
    @staticmethod
    def hasDefaultAlphaBuffer() -> bool: ...
    def closeEvent(self, a0: typing.Optional[QtGui.QCloseEvent]) -> None: ...
    def tabletEvent(self, a0: typing.Optional[QtGui.QTabletEvent]) -> None: ...
    def wheelEvent(self, a0: typing.Optional[QtGui.QWheelEvent]) -> None: ...
    def mouseMoveEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseDoubleClickEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def keyReleaseEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def event(self, a0: typing.Optional[QtCore.QEvent]) -> bool: ...
    def focusOutEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def focusInEvent(self, a0: typing.Optional[QtGui.QFocusEvent]) -> None: ...
    def hideEvent(self, a0: typing.Optional[QtGui.QHideEvent]) -> None: ...
    def showEvent(self, a0: typing.Optional[QtGui.QShowEvent]) -> None: ...
    def resizeEvent(self, a0: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    def exposeEvent(self, a0: typing.Optional[QtGui.QExposeEvent]) -> None: ...
    def releaseResources(self) -> None: ...
    def update(self) -> None: ...
    colorChanged: typing.ClassVar[QtCore.pyqtSignal]
    afterRendering: typing.ClassVar[QtCore.pyqtSignal]
    beforeRendering: typing.ClassVar[QtCore.pyqtSignal]
    beforeSynchronizing: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphInvalidated: typing.ClassVar[QtCore.pyqtSignal]
    sceneGraphInitialized: typing.ClassVar[QtCore.pyqtSignal]
    frameSwapped: typing.ClassVar[QtCore.pyqtSignal]
    def isPersistentSceneGraph(self) -> bool: ...
    def setPersistentSceneGraph(self, persistent: bool) -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def createTextureFromImage(self, image: QtGui.QImage) -> typing.Optional['QSGTexture']: ...
    @typing.overload
    def createTextureFromImage(self, image: QtGui.QImage, options: 'QQuickWindow.CreateTextureOption') -> typing.Optional['QSGTexture']: ...
    def createTextNode(self) -> typing.Optional['QSGTextNode']: ...
    def incubationController(self) -> typing.Optional[QtQml.QQmlIncubationController]: ...
    def renderTarget(self) -> QQuickRenderTarget: ...
    def setRenderTarget(self, target: QQuickRenderTarget) -> None: ...
    def grabWindow(self) -> QtGui.QImage: ...
    def mouseGrabberItem(self) -> typing.Optional[QQuickItem]: ...
    def focusObject(self) -> typing.Optional[QtCore.QObject]: ...
    def activeFocusItem(self) -> typing.Optional[QQuickItem]: ...
    def contentItem(self) -> typing.Optional[QQuickItem]: ...


class QQuickView(QQuickWindow):

    class Status(enum.Enum):
        Null = ... # type: QQuickView.Status
        Ready = ... # type: QQuickView.Status
        Loading = ... # type: QQuickView.Status
        Error = ... # type: QQuickView.Status

    class ResizeMode(enum.Enum):
        SizeViewToRootObject = ... # type: QQuickView.ResizeMode
        SizeRootObjectToView = ... # type: QQuickView.ResizeMode

    @typing.overload
    def __init__(self, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...
    @typing.overload
    def __init__(self, engine: typing.Optional[QtQml.QQmlEngine], parent: typing.Optional[QtGui.QWindow]) -> None: ...
    @typing.overload
    def __init__(self, source: QtCore.QUrl, parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...
    @typing.overload
    def __init__(self, uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], parent: typing.Optional[QtGui.QWindow] = ...) -> None: ...

    def mouseMoveEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mouseReleaseEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def mousePressEvent(self, a0: typing.Optional[QtGui.QMouseEvent]) -> None: ...
    def keyReleaseEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def keyPressEvent(self, a0: typing.Optional[QtGui.QKeyEvent]) -> None: ...
    def timerEvent(self, a0: typing.Optional[QtCore.QTimerEvent]) -> None: ...
    def resizeEvent(self, a0: typing.Optional[QtGui.QResizeEvent]) -> None: ...
    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    def loadFromModule(self, uri: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], typeName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> None: ...
    def setInitialProperties(self, initialProperties: dict[typing.Optional[str], typing.Any]) -> None: ...
    def setSource(self, a0: QtCore.QUrl) -> None: ...
    def initialSize(self) -> QtCore.QSize: ...
    def errors(self) -> list[QtQml.QQmlError]: ...
    def status(self) -> 'QQuickView.Status': ...
    def setResizeMode(self, a0: 'QQuickView.ResizeMode') -> None: ...
    def resizeMode(self) -> 'QQuickView.ResizeMode': ...
    def rootObject(self) -> typing.Optional[QQuickItem]: ...
    def rootContext(self) -> typing.Optional[QtQml.QQmlContext]: ...
    def engine(self) -> typing.Optional[QtQml.QQmlEngine]: ...
    def source(self) -> QtCore.QUrl: ...


class QQuickCloseEvent(PyQt6.sip.simplewrapper): ...


class QSGMaterial(PyQt6.sip.wrapper):

    class Flag(enum.Flag):
        Blending = ... # type: QSGMaterial.Flag
        RequiresDeterminant = ... # type: QSGMaterial.Flag
        RequiresFullMatrixExceptTranslate = ... # type: QSGMaterial.Flag
        RequiresFullMatrix = ... # type: QSGMaterial.Flag
        NoBatching = ... # type: QSGMaterial.Flag
        CustomCompileStep = ... # type: QSGMaterial.Flag

    def __init__(self) -> None: ...

    def viewCount(self) -> int: ...
    def setFlag(self, flags: 'QSGMaterial.Flag', enabled: bool = ...) -> None: ...
    def flags(self) -> 'QSGMaterial.Flag': ...
    def compare(self, other: typing.Optional['QSGMaterial']) -> int: ...
    def createShader(self, renderMode: 'QSGRendererInterface.RenderMode') -> typing.Optional['QSGMaterialShader']: ...
    def type(self) -> typing.Optional['QSGMaterialType']: ...


class QSGFlatColorMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def createShader(self, renderMode: 'QSGRendererInterface.RenderMode') -> typing.Optional['QSGMaterialShader']: ...
    def type(self) -> typing.Optional['QSGMaterialType']: ...


class QSGGeometry(PyQt6.sip.wrapper):

    class Type(enum.Enum):
        ByteType = ... # type: QSGGeometry.Type
        UnsignedByteType = ... # type: QSGGeometry.Type
        ShortType = ... # type: QSGGeometry.Type
        UnsignedShortType = ... # type: QSGGeometry.Type
        IntType = ... # type: QSGGeometry.Type
        UnsignedIntType = ... # type: QSGGeometry.Type
        FloatType = ... # type: QSGGeometry.Type
        Bytes2Type = ... # type: QSGGeometry.Type
        Bytes3Type = ... # type: QSGGeometry.Type
        Bytes4Type = ... # type: QSGGeometry.Type
        DoubleType = ... # type: QSGGeometry.Type

    class DrawingMode(enum.IntEnum):
        DrawPoints = ... # type: QSGGeometry.DrawingMode
        DrawLines = ... # type: QSGGeometry.DrawingMode
        DrawLineLoop = ... # type: QSGGeometry.DrawingMode
        DrawLineStrip = ... # type: QSGGeometry.DrawingMode
        DrawTriangles = ... # type: QSGGeometry.DrawingMode
        DrawTriangleStrip = ... # type: QSGGeometry.DrawingMode
        DrawTriangleFan = ... # type: QSGGeometry.DrawingMode

    class AttributeType(enum.Enum):
        UnknownAttribute = ... # type: QSGGeometry.AttributeType
        PositionAttribute = ... # type: QSGGeometry.AttributeType
        ColorAttribute = ... # type: QSGGeometry.AttributeType
        TexCoordAttribute = ... # type: QSGGeometry.AttributeType
        TexCoord1Attribute = ... # type: QSGGeometry.AttributeType
        TexCoord2Attribute = ... # type: QSGGeometry.AttributeType

    class DataPattern(enum.Enum):
        AlwaysUploadPattern = ... # type: QSGGeometry.DataPattern
        StreamPattern = ... # type: QSGGeometry.DataPattern
        DynamicPattern = ... # type: QSGGeometry.DataPattern
        StaticPattern = ... # type: QSGGeometry.DataPattern

    class Attribute(PyQt6.sip.simplewrapper):

        attributeType = ... # type: 'QSGGeometry.AttributeType'
        isVertexCoordinate = ... # type: int
        position = ... # type: int
        tupleSize = ... # type: int
        type = ... # type: int

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.Attribute') -> None: ...

        @staticmethod
        def createWithAttributeType(pos: int, tupleSize: int, primitiveType: int, attributeType: 'QSGGeometry.AttributeType') -> 'QSGGeometry.Attribute': ...
        @staticmethod
        def create(pos: int, tupleSize: int, primitiveType: int, isPosition: bool = ...) -> 'QSGGeometry.Attribute': ...

    class AttributeSet(PyQt6.sip.simplewrapper):

        attributes = ... # type: PyQt6.sip.array
        count = ... # type: int
        stride = ... # type: int

        def __init__(self, attributes: collections.abc.Iterable['QSGGeometry.Attribute'], stride: int = ...) -> None: ...

    class Point2D(PyQt6.sip.simplewrapper):

        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.Point2D') -> None: ...

        def set(self, nx: float, ny: float) -> None: ...

    class TexturedPoint2D(PyQt6.sip.simplewrapper):

        tx = ... # type: float
        ty = ... # type: float
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.TexturedPoint2D') -> None: ...

        def set(self, nx: float, ny: float, ntx: float, nty: float) -> None: ...

    class ColoredPoint2D(PyQt6.sip.simplewrapper):

        a = ... # type: int
        b = ... # type: int
        g = ... # type: int
        r = ... # type: int
        x = ... # type: float
        y = ... # type: float

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGGeometry.ColoredPoint2D') -> None: ...

        def set(self, nx: float, ny: float, nr: int, ng: int, nb: int, na: int) -> None: ...

    def __init__(self, attribs: 'QSGGeometry.AttributeSet', vertexCount: int, indexCount: int = ..., indexType: int = ...) -> None: ...

    @staticmethod
    def updateColoredRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF) -> None: ...
    def sizeOfIndex(self) -> int: ...
    def vertexDataAsColoredPoint2D(self) -> PyQt6.sip.array: ...
    def vertexDataAsTexturedPoint2D(self) -> PyQt6.sip.array: ...
    def vertexDataAsPoint2D(self) -> PyQt6.sip.array: ...
    def indexDataAsUShort(self) -> PyQt6.sip.array: ...
    def indexDataAsUInt(self) -> PyQt6.sip.array: ...
    def setLineWidth(self, w: float) -> None: ...
    def lineWidth(self) -> float: ...
    def markVertexDataDirty(self) -> None: ...
    def markIndexDataDirty(self) -> None: ...
    def vertexDataPattern(self) -> 'QSGGeometry.DataPattern': ...
    def setVertexDataPattern(self, p: 'QSGGeometry.DataPattern') -> None: ...
    def indexDataPattern(self) -> 'QSGGeometry.DataPattern': ...
    def setIndexDataPattern(self, p: 'QSGGeometry.DataPattern') -> None: ...
    @staticmethod
    def updateTexturedRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF, sourceRect: QtCore.QRectF) -> None: ...
    @staticmethod
    def updateRectGeometry(g: typing.Optional['QSGGeometry'], rect: QtCore.QRectF) -> None: ...
    def sizeOfVertex(self) -> int: ...
    def attributes(self) -> PyQt6.sip.array: ...
    def attributeCount(self) -> int: ...
    def indexData(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def indexCount(self) -> int: ...
    def indexType(self) -> int: ...
    def vertexData(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def vertexCount(self) -> int: ...
    def allocate(self, vertexCount: int, indexCount: int = ...) -> None: ...
    def drawingMode(self) -> int: ...
    def setDrawingMode(self, mode: int) -> None: ...
    @staticmethod
    def defaultAttributes_ColoredPoint2D() -> 'QSGGeometry.AttributeSet': ...
    @staticmethod
    def defaultAttributes_TexturedPoint2D() -> 'QSGGeometry.AttributeSet': ...
    @staticmethod
    def defaultAttributes_Point2D() -> 'QSGGeometry.AttributeSet': ...


class QSGNode(PyQt6.sip.wrapper):

    class DirtyStateBit(enum.Flag):
        DirtyMatrix = ... # type: QSGNode.DirtyStateBit
        DirtyNodeAdded = ... # type: QSGNode.DirtyStateBit
        DirtyNodeRemoved = ... # type: QSGNode.DirtyStateBit
        DirtyGeometry = ... # type: QSGNode.DirtyStateBit
        DirtyMaterial = ... # type: QSGNode.DirtyStateBit
        DirtyOpacity = ... # type: QSGNode.DirtyStateBit

    class Flag(enum.Flag):
        OwnedByParent = ... # type: QSGNode.Flag
        UsePreprocess = ... # type: QSGNode.Flag
        OwnsGeometry = ... # type: QSGNode.Flag
        OwnsMaterial = ... # type: QSGNode.Flag
        OwnsOpaqueMaterial = ... # type: QSGNode.Flag

    class NodeType(enum.Enum):
        BasicNodeType = ... # type: QSGNode.NodeType
        GeometryNodeType = ... # type: QSGNode.NodeType
        TransformNodeType = ... # type: QSGNode.NodeType
        ClipNodeType = ... # type: QSGNode.NodeType
        OpacityNodeType = ... # type: QSGNode.NodeType

    def __init__(self) -> None: ...

    def preprocess(self) -> None: ...
    def setFlags(self, a0: 'QSGNode.Flag', enabled: bool = ...) -> None: ...
    def setFlag(self, a0: 'QSGNode.Flag', enabled: bool = ...) -> None: ...
    def flags(self) -> 'QSGNode.Flag': ...
    def isSubtreeBlocked(self) -> bool: ...
    def markDirty(self, bits: 'QSGNode.DirtyStateBit') -> None: ...
    def type(self) -> 'QSGNode.NodeType': ...
    def previousSibling(self) -> typing.Optional['QSGNode']: ...
    def nextSibling(self) -> typing.Optional['QSGNode']: ...
    def lastChild(self) -> typing.Optional['QSGNode']: ...
    def firstChild(self) -> typing.Optional['QSGNode']: ...
    def childAtIndex(self, i: int) -> typing.Optional['QSGNode']: ...
    def __len__(self) -> int: ...
    def childCount(self) -> int: ...
    def insertChildNodeAfter(self, node: typing.Optional['QSGNode'], after: typing.Optional['QSGNode']) -> None: ...
    def insertChildNodeBefore(self, node: typing.Optional['QSGNode'], before: typing.Optional['QSGNode']) -> None: ...
    def appendChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def prependChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def removeAllChildNodes(self) -> None: ...
    def removeChildNode(self, node: typing.Optional['QSGNode']) -> None: ...
    def parent(self) -> typing.Optional['QSGNode']: ...


class QSGBasicGeometryNode(QSGNode):

    def geometry(self) -> typing.Optional[QSGGeometry]: ...
    def setGeometry(self, geometry: typing.Optional[QSGGeometry]) -> None: ...


class QSGGeometryNode(QSGBasicGeometryNode):

    def __init__(self) -> None: ...

    def opaqueMaterial(self) -> typing.Optional[QSGMaterial]: ...
    def setOpaqueMaterial(self, material: typing.Optional[QSGMaterial]) -> None: ...
    def material(self) -> typing.Optional[QSGMaterial]: ...
    def setMaterial(self, material: typing.Optional[QSGMaterial]) -> None: ...


class QSGImageNode(QSGGeometryNode):

    class TextureCoordinatesTransformFlag(enum.Flag):
        NoTransform = ... # type: QSGImageNode.TextureCoordinatesTransformFlag
        MirrorHorizontally = ... # type: QSGImageNode.TextureCoordinatesTransformFlag
        MirrorVertically = ... # type: QSGImageNode.TextureCoordinatesTransformFlag

    def anisotropyLevel(self) -> 'QSGTexture.AnisotropyLevel': ...
    def setAnisotropyLevel(self, level: 'QSGTexture.AnisotropyLevel') -> None: ...
    @staticmethod
    def rebuildGeometry(g: typing.Optional[QSGGeometry], texture: typing.Optional['QSGTexture'], rect: QtCore.QRectF, sourceRect: QtCore.QRectF, texCoordMode: 'QSGImageNode.TextureCoordinatesTransformFlag') -> None: ...
    def ownsTexture(self) -> bool: ...
    def setOwnsTexture(self, owns: bool) -> None: ...
    def textureCoordinatesTransform(self) -> 'QSGImageNode.TextureCoordinatesTransformFlag': ...
    def setTextureCoordinatesTransform(self, mode: 'QSGImageNode.TextureCoordinatesTransformFlag') -> None: ...
    def mipmapFiltering(self) -> 'QSGTexture.Filtering': ...
    def setMipmapFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def texture(self) -> typing.Optional['QSGTexture']: ...
    def setTexture(self, texture: typing.Optional['QSGTexture']) -> None: ...
    def sourceRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setSourceRect(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGMaterialShader(PyQt6.sip.simplewrapper):

    class Stage(enum.Enum):
        VertexStage = ... # type: QSGMaterialShader.Stage
        FragmentStage = ... # type: QSGMaterialShader.Stage

    class Flag(enum.Flag):
        UpdatesGraphicsPipelineState = ... # type: QSGMaterialShader.Flag

    class RenderState(PyQt6.sip.simplewrapper):

        class DirtyState(enum.Flag):
            DirtyMatrix = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyOpacity = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyCachedMaterialData = ... # type: QSGMaterialShader.RenderState.DirtyState
            DirtyAll = ... # type: QSGMaterialShader.RenderState.DirtyState

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGMaterialShader.RenderState') -> None: ...

        def uniformData(self) -> typing.Optional[QtCore.QByteArray]: ...
        def devicePixelRatio(self) -> float: ...
        def determinant(self) -> float: ...
        def deviceRect(self) -> QtCore.QRect: ...
        def viewportRect(self) -> QtCore.QRect: ...
        def projectionMatrix(self) -> QtGui.QMatrix4x4: ...
        def modelViewMatrix(self) -> QtGui.QMatrix4x4: ...
        def combinedMatrix(self) -> QtGui.QMatrix4x4: ...
        def opacity(self) -> float: ...
        def isOpacityDirty(self) -> bool: ...
        def isMatrixDirty(self) -> bool: ...
        def dirtyStates(self) -> 'QSGMaterialShader.RenderState.DirtyState': ...

    class GraphicsPipelineState(PyQt6.sip.simplewrapper):

        class PolygonMode(enum.Enum):
            Fill = ... # type: QSGMaterialShader.GraphicsPipelineState.PolygonMode
            Line = ... # type: QSGMaterialShader.GraphicsPipelineState.PolygonMode

        class CullMode(enum.Enum):
            CullNone = ... # type: QSGMaterialShader.GraphicsPipelineState.CullMode
            CullFront = ... # type: QSGMaterialShader.GraphicsPipelineState.CullMode
            CullBack = ... # type: QSGMaterialShader.GraphicsPipelineState.CullMode

        class ColorMaskComponent(enum.Flag):
            R = ... # type: QSGMaterialShader.GraphicsPipelineState.ColorMaskComponent
            G = ... # type: QSGMaterialShader.GraphicsPipelineState.ColorMaskComponent
            B = ... # type: QSGMaterialShader.GraphicsPipelineState.ColorMaskComponent
            A = ... # type: QSGMaterialShader.GraphicsPipelineState.ColorMaskComponent

        class BlendOp(enum.Enum):
            Add = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendOp
            Subtract = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendOp
            ReverseSubtract = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendOp
            Min = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendOp
            Max = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendOp

        class BlendFactor(enum.Enum):
            Zero = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            One = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            SrcColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusSrcColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            DstColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusDstColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            SrcAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusSrcAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            DstAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusDstAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            ConstantColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusConstantColor = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            ConstantAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusConstantAlpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            SrcAlphaSaturate = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            Src1Color = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusSrc1Color = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            Src1Alpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor
            OneMinusSrc1Alpha = ... # type: QSGMaterialShader.GraphicsPipelineState.BlendFactor

        blendConstant = ... # type: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]
        blendEnable = ... # type: bool
        colorWrite = ... # type: 'QSGMaterialShader.GraphicsPipelineState.ColorMaskComponent'
        cullMode = ... # type: 'QSGMaterialShader.GraphicsPipelineState.CullMode'
        dstAlpha = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendFactor'
        dstColor = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendFactor'
        opAlpha = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendOp'
        opColor = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendOp'
        polygonMode = ... # type: 'QSGMaterialShader.GraphicsPipelineState.PolygonMode'
        separateBlendFactors = ... # type: bool
        srcAlpha = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendFactor'
        srcColor = ... # type: 'QSGMaterialShader.GraphicsPipelineState.BlendFactor'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QSGMaterialShader.GraphicsPipelineState') -> None: ...

    def __init__(self) -> None: ...

    @typing.overload
    def setShaderFileName(self, stage: 'QSGMaterialShader.Stage', filename: typing.Optional[str]) -> None: ...
    @typing.overload
    def setShaderFileName(self, stage: 'QSGMaterialShader.Stage', filename: typing.Optional[str], viewCount: int) -> None: ...
    def combinedImageSamplerCount(self, binding: int) -> int: ...
    def setFlags(self, flags: 'QSGMaterialShader.Flag') -> None: ...
    def setFlag(self, flags: 'QSGMaterialShader.Flag', on: bool = ...) -> None: ...
    def flags(self) -> 'QSGMaterialShader.Flag': ...
    def updateGraphicsPipelineState(self, state: 'QSGMaterialShader.RenderState', ps: typing.Optional['QSGMaterialShader.GraphicsPipelineState'], newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> bool: ...
    def updateSampledImage(self, state: 'QSGMaterialShader.RenderState', binding: int, newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> typing.Optional['QSGTexture']: ...
    def updateUniformData(self, state: 'QSGMaterialShader.RenderState', newMaterial: typing.Optional[QSGMaterial], oldMaterial: typing.Optional[QSGMaterial]) -> bool: ...


class QSGMaterialType(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSGMaterialType') -> None: ...


class QSGClipNode(QSGBasicGeometryNode):

    def __init__(self) -> None: ...

    def clipRect(self) -> QtCore.QRectF: ...
    def setClipRect(self, a0: QtCore.QRectF) -> None: ...
    def isRectangular(self) -> bool: ...
    def setIsRectangular(self, rectHint: bool) -> None: ...


class QSGTransformNode(QSGNode):

    def __init__(self) -> None: ...

    def matrix(self) -> QtGui.QMatrix4x4: ...
    def setMatrix(self, matrix: QtGui.QMatrix4x4) -> None: ...


class QSGOpacityNode(QSGNode):

    def __init__(self) -> None: ...

    def opacity(self) -> float: ...
    def setOpacity(self, opacity: float) -> None: ...


class QSGRectangleNode(QSGGeometryNode):

    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGRendererInterface(PyQt6.sip.simplewrapper):

    class RenderMode(enum.Enum):
        RenderMode2D = ... # type: QSGRendererInterface.RenderMode
        RenderMode2DNoDepthBuffer = ... # type: QSGRendererInterface.RenderMode
        RenderMode3D = ... # type: QSGRendererInterface.RenderMode

    class ShaderSourceType(enum.Flag):
        ShaderSourceString = ... # type: QSGRendererInterface.ShaderSourceType
        ShaderSourceFile = ... # type: QSGRendererInterface.ShaderSourceType
        ShaderByteCode = ... # type: QSGRendererInterface.ShaderSourceType

    class ShaderCompilationType(enum.Flag):
        RuntimeCompilation = ... # type: QSGRendererInterface.ShaderCompilationType
        OfflineCompilation = ... # type: QSGRendererInterface.ShaderCompilationType

    class ShaderType(enum.Enum):
        UnknownShadingLanguage = ... # type: QSGRendererInterface.ShaderType
        GLSL = ... # type: QSGRendererInterface.ShaderType
        HLSL = ... # type: QSGRendererInterface.ShaderType
        RhiShader = ... # type: QSGRendererInterface.ShaderType

    class Resource(enum.Enum):
        DeviceResource = ... # type: QSGRendererInterface.Resource
        CommandQueueResource = ... # type: QSGRendererInterface.Resource
        CommandListResource = ... # type: QSGRendererInterface.Resource
        PainterResource = ... # type: QSGRendererInterface.Resource
        RhiResource = ... # type: QSGRendererInterface.Resource
        PhysicalDeviceResource = ... # type: QSGRendererInterface.Resource
        OpenGLContextResource = ... # type: QSGRendererInterface.Resource
        DeviceContextResource = ... # type: QSGRendererInterface.Resource
        CommandEncoderResource = ... # type: QSGRendererInterface.Resource
        VulkanInstanceResource = ... # type: QSGRendererInterface.Resource
        RenderPassResource = ... # type: QSGRendererInterface.Resource
        RhiSwapchainResource = ... # type: QSGRendererInterface.Resource
        RhiRedirectCommandBuffer = ... # type: QSGRendererInterface.Resource
        RhiRedirectRenderTarget = ... # type: QSGRendererInterface.Resource
        RedirectPaintDevice = ... # type: QSGRendererInterface.Resource
        GraphicsQueueFamilyIndexResource = ... # type: QSGRendererInterface.Resource
        GraphicsQueueIndexResource = ... # type: QSGRendererInterface.Resource

    class GraphicsApi(enum.Enum):
        Unknown = ... # type: QSGRendererInterface.GraphicsApi
        Software = ... # type: QSGRendererInterface.GraphicsApi
        OpenGL = ... # type: QSGRendererInterface.GraphicsApi
        OpenVG = ... # type: QSGRendererInterface.GraphicsApi
        OpenGLRhi = ... # type: QSGRendererInterface.GraphicsApi
        Direct3D11Rhi = ... # type: QSGRendererInterface.GraphicsApi
        VulkanRhi = ... # type: QSGRendererInterface.GraphicsApi
        MetalRhi = ... # type: QSGRendererInterface.GraphicsApi
        NullRhi = ... # type: QSGRendererInterface.GraphicsApi
        Direct3D11 = ... # type: QSGRendererInterface.GraphicsApi
        Vulkan = ... # type: QSGRendererInterface.GraphicsApi
        Metal = ... # type: QSGRendererInterface.GraphicsApi
        Direct3D12 = ... # type: QSGRendererInterface.GraphicsApi
        Null = ... # type: QSGRendererInterface.GraphicsApi

    @staticmethod
    def isApiRhiBased(api: 'QSGRendererInterface.GraphicsApi') -> bool: ...
    def shaderSourceType(self) -> 'QSGRendererInterface.ShaderSourceType': ...
    def shaderCompilationType(self) -> 'QSGRendererInterface.ShaderCompilationType': ...
    def shaderType(self) -> 'QSGRendererInterface.ShaderType': ...
    @typing.overload
    def getResource(self, window: typing.Optional[QQuickWindow], resource: 'QSGRendererInterface.Resource') -> typing.Optional[PyQt6.sip.voidptr]: ...
    @typing.overload
    def getResource(self, window: typing.Optional[QQuickWindow], resource: typing.Optional[str]) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def graphicsApi(self) -> 'QSGRendererInterface.GraphicsApi': ...


class QSGRenderNode(QSGNode):

    class RenderingFlag(enum.Flag):
        BoundedRectRendering = ... # type: QSGRenderNode.RenderingFlag
        DepthAwareRendering = ... # type: QSGRenderNode.RenderingFlag
        OpaqueRendering = ... # type: QSGRenderNode.RenderingFlag

    class StateFlag(enum.Flag):
        DepthState = ... # type: QSGRenderNode.StateFlag
        StencilState = ... # type: QSGRenderNode.StateFlag
        ScissorState = ... # type: QSGRenderNode.StateFlag
        ColorState = ... # type: QSGRenderNode.StateFlag
        BlendState = ... # type: QSGRenderNode.StateFlag
        CullState = ... # type: QSGRenderNode.StateFlag
        ViewportState = ... # type: QSGRenderNode.StateFlag
        RenderTargetState = ... # type: QSGRenderNode.StateFlag

    class RenderState(PyQt6.sip.simplewrapper):

        def get(self, state: typing.Optional[str]) -> typing.Optional[PyQt6.sip.voidptr]: ...
        def clipRegion(self) -> typing.Optional[QtGui.QRegion]: ...
        def stencilEnabled(self) -> bool: ...
        def stencilValue(self) -> int: ...
        def scissorEnabled(self) -> bool: ...
        def scissorRect(self) -> QtCore.QRect: ...
        def projectionMatrix(self) -> typing.Optional[QtGui.QMatrix4x4]: ...

    def __init__(self) -> None: ...

    def projectionMatrix(self) -> typing.Optional[QtGui.QMatrix4x4]: ...
    def prepare(self) -> None: ...
    def inheritedOpacity(self) -> float: ...
    def clipList(self) -> typing.Optional[QSGClipNode]: ...
    def matrix(self) -> typing.Optional[QtGui.QMatrix4x4]: ...
    def rect(self) -> QtCore.QRectF: ...
    def flags(self) -> 'QSGRenderNode.RenderingFlag': ...
    def releaseResources(self) -> None: ...
    def render(self, state: typing.Optional['QSGRenderNode.RenderState']) -> None: ...
    def changedStates(self) -> 'QSGRenderNode.StateFlag': ...


class QSGSimpleRectNode(QSGGeometryNode):

    @typing.overload
    def __init__(self, rect: QtCore.QRectF, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...

    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGSimpleTextureNode(QSGGeometryNode):

    class TextureCoordinatesTransformFlag(enum.Flag):
        NoTransform = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag
        MirrorHorizontally = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag
        MirrorVertically = ... # type: QSGSimpleTextureNode.TextureCoordinatesTransformFlag

    def __init__(self) -> None: ...

    def sourceRect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setSourceRect(self, r: QtCore.QRectF) -> None: ...
    @typing.overload
    def setSourceRect(self, x: float, y: float, w: float, h: float) -> None: ...
    def ownsTexture(self) -> bool: ...
    def setOwnsTexture(self, owns: bool) -> None: ...
    def textureCoordinatesTransform(self) -> 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag': ...
    def setTextureCoordinatesTransform(self, mode: 'QSGSimpleTextureNode.TextureCoordinatesTransformFlag') -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filtering: 'QSGTexture.Filtering') -> None: ...
    def texture(self) -> typing.Optional['QSGTexture']: ...
    def setTexture(self, texture: typing.Optional['QSGTexture']) -> None: ...
    def rect(self) -> QtCore.QRectF: ...
    @typing.overload
    def setRect(self, rect: QtCore.QRectF) -> None: ...
    @typing.overload
    def setRect(self, x: float, y: float, w: float, h: float) -> None: ...


class QSGTextNode(QSGTransformNode):

    class TextStyle(enum.Enum):
        Normal = ... # type: QSGTextNode.TextStyle
        Outline = ... # type: QSGTextNode.TextStyle
        Raised = ... # type: QSGTextNode.TextStyle
        Sunken = ... # type: QSGTextNode.TextStyle

    class RenderType(enum.Enum):
        QtRendering = ... # type: QSGTextNode.RenderType
        NativeRendering = ... # type: QSGTextNode.RenderType
        CurveRendering = ... # type: QSGTextNode.RenderType

    def viewport(self) -> QtCore.QRectF: ...
    def setViewport(self, viewport: QtCore.QRectF) -> None: ...
    def clear(self) -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, a0: 'QSGTexture.Filtering') -> None: ...
    def renderTypeQuality(self) -> int: ...
    def setRenderTypeQuality(self, renderTypeQuality: int) -> None: ...
    def renderType(self) -> 'QSGTextNode.RenderType': ...
    def setRenderType(self, renderType: 'QSGTextNode.RenderType') -> None: ...
    def selectionTextColor(self) -> QtGui.QColor: ...
    def setSelectionTextColor(self, selectionTextColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def selectionColor(self) -> QtGui.QColor: ...
    def setSelectionColor(self, selectionColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def linkColor(self) -> QtGui.QColor: ...
    def setLinkColor(self, linkColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def styleColor(self) -> QtGui.QColor: ...
    def setStyleColor(self, styleColor: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def textStyle(self) -> 'QSGTextNode.TextStyle': ...
    def setTextStyle(self, textStyle: 'QSGTextNode.TextStyle') -> None: ...
    def color(self) -> QtGui.QColor: ...
    def setColor(self, color: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]) -> None: ...
    def addTextLayout(self, position: QtCore.QPointF, layout: typing.Optional[QtGui.QTextLayout], selectionStart: int = ..., selectionCount: int = ..., lineStart: int = ..., lineCount: int = ...) -> None: ...
    def addTextDocument(self, position: QtCore.QPointF, document: typing.Optional[QtGui.QTextDocument], selectionStart: int = ..., selectionCount: int = ...) -> None: ...


class QSGTexture(QtCore.QObject):

    class AnisotropyLevel(enum.Enum):
        AnisotropyNone = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy2x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy4x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy8x = ... # type: QSGTexture.AnisotropyLevel
        Anisotropy16x = ... # type: QSGTexture.AnisotropyLevel

    class Filtering(enum.Enum):
        None_ = ... # type: QSGTexture.Filtering
        Nearest = ... # type: QSGTexture.Filtering
        Linear = ... # type: QSGTexture.Filtering

    class WrapMode(enum.Enum):
        Repeat = ... # type: QSGTexture.WrapMode
        ClampToEdge = ... # type: QSGTexture.WrapMode
        MirroredRepeat = ... # type: QSGTexture.WrapMode

    def __init__(self) -> None: ...

    def comparisonKey(self) -> int: ...
    def anisotropyLevel(self) -> 'QSGTexture.AnisotropyLevel': ...
    def setAnisotropyLevel(self, level: 'QSGTexture.AnisotropyLevel') -> None: ...
    def convertToNormalizedSourceRect(self, rect: QtCore.QRectF) -> QtCore.QRectF: ...
    def verticalWrapMode(self) -> 'QSGTexture.WrapMode': ...
    def setVerticalWrapMode(self, vwrap: 'QSGTexture.WrapMode') -> None: ...
    def horizontalWrapMode(self) -> 'QSGTexture.WrapMode': ...
    def setHorizontalWrapMode(self, hwrap: 'QSGTexture.WrapMode') -> None: ...
    def filtering(self) -> 'QSGTexture.Filtering': ...
    def setFiltering(self, filter: 'QSGTexture.Filtering') -> None: ...
    def mipmapFiltering(self) -> 'QSGTexture.Filtering': ...
    def setMipmapFiltering(self, filter: 'QSGTexture.Filtering') -> None: ...
    def isAtlasTexture(self) -> bool: ...
    def normalizedTextureSubRect(self) -> QtCore.QRectF: ...
    def hasMipmaps(self) -> bool: ...
    def hasAlphaChannel(self) -> bool: ...
    def textureSize(self) -> QtCore.QSize: ...


class QSGDynamicTexture(QSGTexture):

    def __init__(self) -> None: ...

    def updateTexture(self) -> bool: ...


class QNativeInterface(PyQt6.sip.simplewrapper):

    class QSGOpenGLTexture(PyQt6.sip.simplewrapper):

        @staticmethod
        def fromNative(textureId: int, window: typing.Optional[QQuickWindow], size: QtCore.QSize, options: QQuickWindow.CreateTextureOption = ...) -> typing.Optional[QSGTexture]: ...
        def nativeTexture(self) -> int: ...


class QSGOpaqueTextureMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def anisotropyLevel(self) -> QSGTexture.AnisotropyLevel: ...
    def setAnisotropyLevel(self, level: QSGTexture.AnisotropyLevel) -> None: ...
    def verticalWrapMode(self) -> QSGTexture.WrapMode: ...
    def setVerticalWrapMode(self, mode: QSGTexture.WrapMode) -> None: ...
    def horizontalWrapMode(self) -> QSGTexture.WrapMode: ...
    def setHorizontalWrapMode(self, mode: QSGTexture.WrapMode) -> None: ...
    def filtering(self) -> QSGTexture.Filtering: ...
    def setFiltering(self, filtering: QSGTexture.Filtering) -> None: ...
    def mipmapFiltering(self) -> QSGTexture.Filtering: ...
    def setMipmapFiltering(self, filtering: QSGTexture.Filtering) -> None: ...
    def texture(self) -> typing.Optional[QSGTexture]: ...
    def setTexture(self, texture: typing.Optional[QSGTexture]) -> None: ...
    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
    def createShader(self, renderMode: QSGRendererInterface.RenderMode) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...


class QSGTextureMaterial(QSGOpaqueTextureMaterial):

    def __init__(self) -> None: ...

    def createShader(self, renderMode: QSGRendererInterface.RenderMode) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...


class QSGTextureProvider(QtCore.QObject):

    def __init__(self) -> None: ...

    textureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def texture(self) -> typing.Optional[QSGTexture]: ...


class QSGVertexColorMaterial(QSGMaterial):

    def __init__(self) -> None: ...

    def createShader(self, renderMode: QSGRendererInterface.RenderMode) -> typing.Optional[QSGMaterialShader]: ...
    def type(self) -> typing.Optional[QSGMaterialType]: ...
    def compare(self, other: typing.Optional[QSGMaterial]) -> int: ...
