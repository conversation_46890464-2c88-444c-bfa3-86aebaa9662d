// qqmlpropertymap.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlPropertyMap : public QObject
{
%TypeHeaderCode
#include <qqmlpropertymap.h>
%End

public:
    explicit QQmlPropertyMap(QObject *parent /TransferThis/ = 0);
    virtual ~QQmlPropertyMap();
    QVariant value(const QString &key) const;
%If (Qt_6_1_0 -)
    void insert(const QVariantHash &values);
%End
    void insert(const QString &key, const QVariant &value);
%If (Qt_6_1_0 -)
    void freeze();
%End
    void clear(const QString &key);
    QStringList keys() const;
    int count() const;
    int size() const /__len__/;
    bool isEmpty() const;
    bool contains(const QString &key) const;
    QVariant operator[](const QString &key) const;

signals:
    void valueChanged(const QString &key, const QVariant &value);

protected:
    virtual QVariant updateValue(const QString &key, const QVariant &input);
};
