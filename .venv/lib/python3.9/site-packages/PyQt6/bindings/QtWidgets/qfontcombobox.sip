// qfontcombobox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFontComboBox : public QComboBox
{
%TypeHeaderCode
#include <qfontcombobox.h>
%End

public:
    enum FontFilter /BaseType=Flag/
    {
        AllFonts,
        ScalableFonts,
        NonScalableFonts,
        MonospacedFonts,
        ProportionalFonts,
    };

    typedef QFlags<QFontComboBox::FontFilter> FontFilters;
    QFontComboBox::FontFilters fontFilters() const;
    explicit QFontComboBox(QWidget *parent /TransferThis/ = 0);
    virtual ~QFontComboBox();
    void setWritingSystem(QFontDatabase::WritingSystem);
    QFontDatabase::WritingSystem writingSystem() const;
    void setFontFilters(QFontComboBox::FontFilters filters);
    QFont currentFont() const;
    virtual QSize sizeHint() const;

public slots:
    void setCurrentFont(const QFont &f);

signals:
    void currentFontChanged(const QFont &f);

protected:
    virtual bool event(QEvent *e);

public:
%If (Qt_6_3_0 -)
    void setSampleTextForSystem(QFontDatabase::WritingSystem writingSystem, const QString &sampleText);
%End
%If (Qt_6_3_0 -)
    QString sampleTextForSystem(QFontDatabase::WritingSystem writingSystem) const;
%End
%If (Qt_6_3_0 -)
    void setSampleTextForFont(const QString &fontFamily, const QString &sampleText);
%End
%If (Qt_6_3_0 -)
    QString sampleTextForFont(const QString &fontFamily) const;
%End
%If (Qt_6_3_0 -)
    void setDisplayFont(const QString &fontFamily, const QFont &font);
%End
%If (Qt_6_3_0 -)
    SIP_PYOBJECT displayFont(const QString &fontFamily) const /TypeHint="Optional[QFont]"/;
%MethodCode
        std::optional<QFont> f = sipCpp->displayFont(*a0);
        
        if (f)
        {
            sipRes = sipConvertFromNewType(new QFont(f.value()), sipType_QFont, NULL);
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End

%End
};
