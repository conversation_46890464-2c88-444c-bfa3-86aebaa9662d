// qabstractscrollarea.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractScrollArea : public QFrame
{
%TypeHeaderCode
#include <qabstractscrollarea.h>
%End

public:
    explicit QAbstractScrollArea(QWidget *parent /TransferThis/ = 0);
    virtual ~QAbstractScrollArea();
    Qt::ScrollBarPolicy verticalScrollBarPolicy() const;
    void setVerticalScrollBarPolicy(Qt::ScrollBarPolicy);
    QScrollBar *verticalScrollBar() const /Transfer/;
    Qt::ScrollBarPolicy horizontalScrollBarPolicy() const;
    void setHorizontalScrollBarPolicy(Qt::ScrollBarPolicy);
    QScrollBar *horizontalScrollBar() const /Transfer/;
    QWidget *viewport() const /Transfer/;
    QSize maximumViewportSize() const;
    virtual QSize minimumSizeHint() const;
    virtual QSize sizeHint() const;

protected:
    void setViewportMargins(int left, int top, int right, int bottom);
    void setViewportMargins(const QMargins &margins);
    QMargins viewportMargins() const;
    virtual QSize viewportSizeHint() const;
    virtual bool event(QEvent *);
    virtual bool viewportEvent(QEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual void dragEnterEvent(QDragEnterEvent *);
    virtual void dragMoveEvent(QDragMoveEvent *);
    virtual void dragLeaveEvent(QDragLeaveEvent *);
    virtual void dropEvent(QDropEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
    virtual void scrollContentsBy(int dx, int dy);

public:
    void setVerticalScrollBar(QScrollBar *scrollbar /Transfer/);
    void setHorizontalScrollBar(QScrollBar *scrollbar /Transfer/);
    QWidget *cornerWidget() const;
    void setCornerWidget(QWidget *widget /Transfer/);
    void addScrollBarWidget(QWidget *widget /Transfer/, Qt::Alignment alignment);
    QWidgetList scrollBarWidgets(Qt::Alignment alignment) /Transfer/;
    void setViewport(QWidget *widget /Transfer/);
    virtual void setupViewport(QWidget *viewport);

    enum SizeAdjustPolicy
    {
        AdjustIgnored,
        AdjustToContentsOnFirstShow,
        AdjustToContents,
    };

    QAbstractScrollArea::SizeAdjustPolicy sizeAdjustPolicy() const;
    void setSizeAdjustPolicy(QAbstractScrollArea::SizeAdjustPolicy policy);
};
