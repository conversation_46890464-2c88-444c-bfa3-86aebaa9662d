// qlineedit.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLineEdit : public QWidget
{
%TypeHeaderCode
#include <qlineedit.h>
%End

public:
    explicit QLineEdit(QWidget *parent /TransferThis/ = 0);
    QLineEdit(const QString &contents, QWidget *parent /TransferThis/ = 0);
    virtual ~QLineEdit();
    QString text() const;
    QString displayText() const;
    int maxLength() const;
    void setMaxLength(int);
    void setFrame(bool);
    bool hasFrame() const;

    enum EchoMode
    {
        Normal,
        NoEcho,
        Password,
        PasswordEchoOnEdit,
    };

    QLineEdit::EchoMode echoMode() const;
    void setEchoMode(QLineEdit::EchoMode);
    bool isReadOnly() const;
    void setReadOnly(bool);
    void setValidator(const QValidator * /KeepReference/);
    const QValidator *validator() const;
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    int cursorPosition() const;
    void setCursorPosition(int);
    int cursorPositionAt(const QPoint &pos);
    void setAlignment(Qt::Alignment flag);
    Qt::Alignment alignment() const;
    void cursorForward(bool mark, int steps = 1);
    void cursorBackward(bool mark, int steps = 1);
    void cursorWordForward(bool mark);
    void cursorWordBackward(bool mark);
    void backspace();
    void del();
    void home(bool mark);
    void end(bool mark);
    bool isModified() const;
    void setModified(bool);
    void setSelection(int, int);
    bool hasSelectedText() const;
    QString selectedText() const;
    int selectionStart() const;
    bool isUndoAvailable() const;
    bool isRedoAvailable() const;
    void setDragEnabled(bool b);
    bool dragEnabled() const;
    QString inputMask() const;
    void setInputMask(const QString &inputMask);
    bool hasAcceptableInput() const;
    void setText(const QString &);
    void clear();
    void selectAll();
    void undo();
    void redo();
    void cut();
    void copy() const;
    void paste();
    void deselect();
    void insert(const QString &);
    QMenu *createStandardContextMenu() /Factory/;

signals:
    void textChanged(const QString &);
    void textEdited(const QString &);
    void cursorPositionChanged(int, int);
    void returnPressed();
    void editingFinished();
    void selectionChanged();

protected:
    virtual void initStyleOption(QStyleOptionFrame *option) const;
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void dragEnterEvent(QDragEnterEvent *);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dropEvent(QDropEvent *);
    virtual void changeEvent(QEvent *);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual void inputMethodEvent(QInputMethodEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    QRect cursorRect() const;

public:
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery) const;
    virtual bool event(QEvent *);
    virtual void timerEvent(QTimerEvent *);
    void setCompleter(QCompleter *completer /KeepReference/);
    QCompleter *completer() const;
    void setTextMargins(int left, int top, int right, int bottom);
    void setTextMargins(const QMargins &margins);
    QMargins textMargins() const;
    QString placeholderText() const;
    void setPlaceholderText(const QString &);
    void setCursorMoveStyle(Qt::CursorMoveStyle style);
    Qt::CursorMoveStyle cursorMoveStyle() const;

    enum ActionPosition
    {
        LeadingPosition,
        TrailingPosition,
    };

    void setClearButtonEnabled(bool enable);
    bool isClearButtonEnabled() const;
    void addAction(QAction *);
    void addAction(QAction *action, QLineEdit::ActionPosition position);
    QAction *addAction(const QIcon &icon, QLineEdit::ActionPosition position) /Transfer/;
    QVariant inputMethodQuery(Qt::InputMethodQuery property, QVariant argument) const;
    int selectionEnd() const;
    int selectionLength() const;

signals:
    void inputRejected();
};
