// qgraphicslayoutitem.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsLayoutItem /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qgraphicslayoutitem.h>
%End

public:
    QGraphicsLayoutItem(QGraphicsLayoutItem *parent /TransferThis/ = 0, bool isLayout = false);
    virtual ~QGraphicsLayoutItem();
    void setSizePolicy(const QSizePolicy &policy);
    void setSizePolicy(QSizePolicy::Policy hPolicy, QSizePolicy::Policy vPolicy, QSizePolicy::ControlType controlType = QSizePolicy::DefaultType);
    QSizePolicy sizePolicy() const;
    void setMinimumSize(const QSizeF &size);
    QSizeF minimumSize() const;
    void setMinimumWidth(qreal width);
    void setMinimumHeight(qreal height);
    void setPreferredSize(const QSizeF &size);
    QSizeF preferredSize() const;
    void setPreferredWidth(qreal width);
    void setPreferredHeight(qreal height);
    void setMaximumSize(const QSizeF &size);
    QSizeF maximumSize() const;
    void setMaximumWidth(qreal width);
    void setMaximumHeight(qreal height);
    virtual void setGeometry(const QRectF &rect);
    QRectF geometry() const;
    virtual void getContentsMargins(qreal *left, qreal *top, qreal *right, qreal *bottom) const;
    QRectF contentsRect() const;
    QSizeF effectiveSizeHint(Qt::SizeHint which, const QSizeF &constraint = QSizeF()) const;
    virtual void updateGeometry();
    QGraphicsLayoutItem *parentLayoutItem() const;
    void setParentLayoutItem(QGraphicsLayoutItem *parent /TransferThis/);
    bool isLayout() const;
    void setMinimumSize(qreal aw, qreal ah);
    void setPreferredSize(qreal aw, qreal ah);
    void setMaximumSize(qreal aw, qreal ah);
    qreal minimumWidth() const;
    qreal minimumHeight() const;
    qreal preferredWidth() const;
    qreal preferredHeight() const;
    qreal maximumWidth() const;
    qreal maximumHeight() const;
    QGraphicsItem *graphicsItem() const;
    bool ownedByLayout() const;
    virtual bool isEmpty() const;

protected:
    virtual QSizeF sizeHint(Qt::SizeHint which, const QSizeF &constraint = QSizeF()) const = 0;
    void setGraphicsItem(QGraphicsItem *item);
    void setOwnedByLayout(bool ownedByLayout);

private:
    QGraphicsLayoutItem(const QGraphicsLayoutItem &);
};
