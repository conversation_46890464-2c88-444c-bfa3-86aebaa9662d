// qtextedit.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextEdit : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qtextedit.h>
%End

public:
    struct ExtraSelection
    {
%TypeHeaderCode
#include <qtextedit.h>
%End

        QTextCursor cursor;
        QTextCharFormat format;
    };

    enum LineWrapMode
    {
        NoWrap,
        WidgetWidth,
        FixedPixelWidth,
        FixedColumnWidth,
    };

    enum AutoFormattingFlag /BaseType=Flag/
    {
        AutoNone,
        AutoBulletList,
        AutoAll,
    };

    typedef QFlags<QTextEdit::AutoFormattingFlag> AutoFormatting;
    explicit QTextEdit(QWidget *parent /TransferThis/ = 0);
    QTextEdit(const QString &text, QWidget *parent /TransferThis/ = 0);
    virtual ~QTextEdit();
    void setDocument(QTextDocument *document /KeepReference/);
    QTextDocument *document() const;
    void setTextCursor(const QTextCursor &cursor);
    QTextCursor textCursor() const;
    bool isReadOnly() const;
    void setReadOnly(bool ro);
    qreal fontPointSize() const;
    QString fontFamily() const;
    int fontWeight() const;
    bool fontUnderline() const;
    bool fontItalic() const;
    QColor textColor() const;
    QFont currentFont() const;
    Qt::Alignment alignment() const;
    void mergeCurrentCharFormat(const QTextCharFormat &modifier);
    void setCurrentCharFormat(const QTextCharFormat &format);
    QTextCharFormat currentCharFormat() const;
    QTextEdit::AutoFormatting autoFormatting() const;
    void setAutoFormatting(QTextEdit::AutoFormatting features);
    bool tabChangesFocus() const;
    void setTabChangesFocus(bool b);
    void setDocumentTitle(const QString &title);
    QString documentTitle() const;
    bool isUndoRedoEnabled() const;
    void setUndoRedoEnabled(bool enable);
    QTextEdit::LineWrapMode lineWrapMode() const;
    void setLineWrapMode(QTextEdit::LineWrapMode mode);
    int lineWrapColumnOrWidth() const;
    void setLineWrapColumnOrWidth(int w);
    QTextOption::WrapMode wordWrapMode() const;
    void setWordWrapMode(QTextOption::WrapMode policy);
    bool find(const QString &exp, QTextDocument::FindFlags options = QTextDocument::FindFlags());
    QString toPlainText() const;
    QString toHtml() const;
    void append(const QString &text);
    void ensureCursorVisible();
    virtual QVariant loadResource(int type, const QUrl &name);
    QMenu *createStandardContextMenu() /Factory/;
    QMenu *createStandardContextMenu(const QPoint &position) /Factory/;
    QTextCursor cursorForPosition(const QPoint &pos) const;
    QRect cursorRect(const QTextCursor &cursor) const;
    QRect cursorRect() const;
    QString anchorAt(const QPoint &pos) const;
    bool overwriteMode() const;
    void setOverwriteMode(bool overwrite);
    bool acceptRichText() const;
    void setAcceptRichText(bool accept);
    void setTextInteractionFlags(Qt::TextInteractionFlags flags);
    Qt::TextInteractionFlags textInteractionFlags() const;
    void setCursorWidth(int width);
    int cursorWidth() const;
    void setExtraSelections(const QList<QTextEdit::ExtraSelection> &selections);
    QList<QTextEdit::ExtraSelection> extraSelections() const;
    bool canPaste() const;
    void moveCursor(QTextCursor::MoveOperation operation, QTextCursor::MoveMode mode = QTextCursor::MoveAnchor);
%If (PyQt_Printer)
    void print(QPagedPaintDevice *printer) const;
%End

public slots:
    void setFontPointSize(qreal s);
    void setFontFamily(const QString &fontFamily);
    void setFontWeight(int w);
    void setFontUnderline(bool b);
    void setFontItalic(bool b);
    void setText(const QString &text);
    void setTextColor(const QColor &c);
    void setCurrentFont(const QFont &f);
    void setAlignment(Qt::Alignment a);
    void setPlainText(const QString &text);
    void setHtml(const QString &text);
    void cut();
    void copy();
    void paste();
    void clear();
    void selectAll();
    void insertPlainText(const QString &text);
    void insertHtml(const QString &text);
    void scrollToAnchor(const QString &name);
    void redo();
    void undo();
    void zoomIn(int range = 1);
    void zoomOut(int range = 1);

signals:
    void textChanged();
    void undoAvailable(bool b);
    void redoAvailable(bool b);
    void currentCharFormatChanged(const QTextCharFormat &format);
    void copyAvailable(bool b);
    void selectionChanged();
    void cursorPositionChanged();

protected:
    virtual bool event(QEvent *e);
    virtual void timerEvent(QTimerEvent *e);
    virtual void keyPressEvent(QKeyEvent *e);
    virtual void keyReleaseEvent(QKeyEvent *e);
    virtual void resizeEvent(QResizeEvent *);
    virtual void paintEvent(QPaintEvent *e);
    virtual void mousePressEvent(QMouseEvent *e);
    virtual void mouseMoveEvent(QMouseEvent *e);
    virtual void mouseReleaseEvent(QMouseEvent *e);
    virtual void mouseDoubleClickEvent(QMouseEvent *e);
    virtual bool focusNextPrevChild(bool next);
    virtual void contextMenuEvent(QContextMenuEvent *e);
    virtual void dragEnterEvent(QDragEnterEvent *e);
    virtual void dragLeaveEvent(QDragLeaveEvent *e);
    virtual void dragMoveEvent(QDragMoveEvent *e);
    virtual void dropEvent(QDropEvent *e);
    virtual void focusInEvent(QFocusEvent *e);
    virtual void focusOutEvent(QFocusEvent *e);
    virtual void showEvent(QShowEvent *);
    virtual void changeEvent(QEvent *e);
    virtual void wheelEvent(QWheelEvent *e);
    virtual QMimeData *createMimeDataFromSelection() const /Factory/;
    virtual bool canInsertFromMimeData(const QMimeData *source) const;
    virtual void insertFromMimeData(const QMimeData *source);
    virtual void inputMethodEvent(QInputMethodEvent *);

public:
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery property) const;

protected:
    virtual void scrollContentsBy(int dx, int dy);

public:
    QColor textBackgroundColor() const;

public slots:
    void setTextBackgroundColor(const QColor &c);

public:
    void setPlaceholderText(const QString &placeholderText);
    QString placeholderText() const;
    bool find(const QRegularExpression &exp, QTextDocument::FindFlags options = QTextDocument::FindFlags());
    QVariant inputMethodQuery(Qt::InputMethodQuery query, QVariant argument) const;
    qreal tabStopDistance() const;
    void setTabStopDistance(qreal distance);
    QString toMarkdown(QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub) const;

public slots:
    void setMarkdown(const QString &markdown);
};
