// qprocess.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Process)

class QProcess : public QIODevice
{
%TypeHeaderCode
#include <qprocess.h>
%End

public:
    enum ExitStatus
    {
        NormalExit,
        CrashExit,
    };

    enum ProcessError
    {
        FailedToStart,
        Crashed,
        Timedout,
        ReadError,
        WriteError,
        UnknownError,
    };

    enum ProcessState
    {
        NotRunning,
        Starting,
        Running,
    };

    enum ProcessChannel
    {
        StandardOutput,
        StandardError,
    };

    enum ProcessChannelMode
    {
        SeparateChannels,
        MergedChannels,
        ForwardedChannels,
        ForwardedOutputChannel,
        ForwardedErrorChannel,
    };

    explicit QProcess(QObject *parent /TransferThis/ = 0);
    virtual ~QProcess();
    void start(const QString &program, const QStringList &arguments = {}, QIODeviceBase::OpenMode mode = QIODeviceBase::ReadWrite) /HoldGIL/;
    void start(QIODeviceBase::OpenMode mode = QIODeviceBase::ReadWrite) /HoldGIL/;
    void startCommand(const QString &command, QIODeviceBase::OpenMode mode = QIODeviceBase::ReadWrite) /HoldGIL/;
    QProcess::ProcessChannel readChannel() const;
    void setReadChannel(QProcess::ProcessChannel channel);
    void closeReadChannel(QProcess::ProcessChannel channel);
    void closeWriteChannel();
    QString workingDirectory() const;
    void setWorkingDirectory(const QString &dir);
    QProcess::ProcessError error() const;
    QProcess::ProcessState state() const;
    bool waitForStarted(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs = 30000) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs = 30000) /ReleaseGIL/;
    bool waitForFinished(int msecs = 30000) /ReleaseGIL/;
    QByteArray readAllStandardOutput() /ReleaseGIL/;
    QByteArray readAllStandardError() /ReleaseGIL/;
    int exitCode() const;
    QProcess::ExitStatus exitStatus() const;
    virtual qint64 bytesToWrite() const;
    virtual bool isSequential() const;
    virtual void close();
    static int execute(const QString &program, const QStringList &arguments = {}) /ReleaseGIL/;
    static bool startDetached(const QString &program, const QStringList &arguments = {}, const QString &workingDirectory = QString(), qint64 *pid = 0);
    bool startDetached(qint64 *pid = 0);
    static QStringList systemEnvironment();
    QProcess::ProcessChannelMode processChannelMode() const;
    void setProcessChannelMode(QProcess::ProcessChannelMode mode);
    void setStandardInputFile(const QString &fileName);
    void setStandardOutputFile(const QString &fileName, QIODeviceBase::OpenMode mode = QIODeviceBase::Truncate);
    void setStandardErrorFile(const QString &fileName, QIODeviceBase::OpenMode mode = QIODeviceBase::Truncate);
    void setStandardOutputProcess(QProcess *destination);

public slots:
    void terminate();
    void kill();

signals:
    void started();
    void finished(int exitCode, QProcess::ExitStatus exitStatus = QProcess::NormalExit);
    void stateChanged(QProcess::ProcessState state);
    void readyReadStandardOutput();
    void readyReadStandardError();
    void errorOccurred(QProcess::ProcessError error);

protected:
    void setProcessState(QProcess::ProcessState state);
    virtual SIP_PYOBJECT readData(qint64 maxlen) /ReleaseGIL,TypeHint="bytes"/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QProcess::readData(s, a0) : sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    virtual qint64 writeData(SIP_PYBUFFER) /ReleaseGIL/ [qint64 (const char *data, qint64 len)];
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipRes = sipSelfWasArg ?
                    sipCpp->QProcess::writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len) :
                    sipCpp->writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
        #else
            sipRes = sipCpp->sipProtectVirt_writeData(sipSelfWasArg, reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
        #endif
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

public:
    void setProcessEnvironment(const QProcessEnvironment &environment);
    QProcessEnvironment processEnvironment() const;
    QString program() const;
    void setProgram(const QString &program);
    QStringList arguments() const;
    void setArguments(const QStringList &arguments);
%If (Windows)
    void setNativeArguments(const QString &arguments);
%End
%If (Windows)
    QString nativeArguments() const;
%End
    virtual bool open(QIODeviceBase::OpenMode mode = QIODeviceBase::ReadWrite) /ReleaseGIL/;

    enum InputChannelMode
    {
        ManagedInputChannel,
        ForwardedInputChannel,
    };

    QProcess::InputChannelMode inputChannelMode() const;
    void setInputChannelMode(QProcess::InputChannelMode mode);
    static QString nullDevice();
    qint64 processId() const;
%If (Qt_6_6_0 -)
%If (Linux || macOS)

    enum class UnixProcessFlag
    {
        ResetSignalHandlers,
        IgnoreSigPipe,
        CloseFileDescriptors,
        UseVFork,
%If (Qt_6_7_0 -)
        CreateNewSession,
%End
%If (Qt_6_7_0 -)
        DisconnectControllingTerminal,
%End
%If (Qt_6_7_0 -)
        ResetIds,
%End
%If (Qt_6_9_0 -)
        DisableCoreDumps,
%End
    };

%End
%End
%If (Qt_6_6_0 -)
%If (Linux || macOS)
    typedef QFlags<QProcess::UnixProcessFlag> UnixProcessFlags;
%End
%End
%If (Qt_6_6_0 -)
%If (Linux || macOS)

    struct UnixProcessParameters
    {
%TypeHeaderCode
#include <qprocess.h>
%End

        QProcess::UnixProcessFlags flags;
        int lowestFileDescriptorToClose;
    };

%End
%End
%If (Qt_6_6_0 -)
%If (Linux || macOS)
    QProcess::UnixProcessParameters unixProcessParameters() const;
%End
%End
%If (Qt_6_6_0 -)
%If (Linux || macOS)
    void setUnixProcessParameters(QProcess::UnixProcessFlags flagsOnly);
%End
%End
%If (Qt_6_6_0 -)
%If (Linux || macOS)
    void setUnixProcessParameters(const QProcess::UnixProcessParameters &params);
%End
%End
};

%End
%If (PyQt_Process)

class QProcessEnvironment
{
%TypeHeaderCode
#include <qprocess.h>
%End

public:
%If (Qt_6_3_0 -)

    enum Initialization
    {
        InheritFromParent,
    };

%End
%If (Qt_6_3_0 -)
    QProcessEnvironment(QProcessEnvironment::Initialization);
%End
    QProcessEnvironment();
    QProcessEnvironment(const QProcessEnvironment &other);
    ~QProcessEnvironment();
%If (- Qt_6_8_0)
    bool operator==(const QProcessEnvironment &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QProcessEnvironment &other) const;
%End
    bool isEmpty() const;
    void clear();
    bool contains(const QString &name) const;
    void insert(const QString &name, const QString &value);
    void insert(const QProcessEnvironment &e);
    void remove(const QString &name);
    QString value(const QString &name, const QString &defaultValue = QString()) const;
    QStringList toStringList() const;
    static QProcessEnvironment systemEnvironment();
    QStringList keys() const;
    void swap(QProcessEnvironment &other /Constrained/);
%If (Qt_6_3_0 -)
    bool inheritsFromParent() const;
%End
};

%End
%If (Qt_6_8_0 -)
bool operator!=(const QProcessEnvironment &lhs, const QProcessEnvironment &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QProcessEnvironment &lhs, const QProcessEnvironment &rhs);
%End
