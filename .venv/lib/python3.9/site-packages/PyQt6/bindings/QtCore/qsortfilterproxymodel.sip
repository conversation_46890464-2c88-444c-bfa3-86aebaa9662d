// qsortfilterproxymodel.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSortFilterProxyModel : public QAbstractProxyModel
{
%TypeHeaderCode
#include <qsortfilterproxymodel.h>
%End

public:
    explicit QSortFilterProxyModel(QObject *parent /TransferThis/ = 0);
    virtual ~QSortFilterProxyModel();
    virtual void setSourceModel(QAbstractItemModel *sourceModel /KeepReference/);
    virtual QModelIndex mapToSource(const QModelIndex &proxyIndex) const;
    virtual QModelIndex mapFromSource(const QModelIndex &sourceIndex) const;
    virtual QItemSelection mapSelectionToSource(const QItemSelection &proxySelection) const;
    virtual QItemSelection mapSelectionFromSource(const QItemSelection &sourceSelection) const;
    QRegularExpression filterRegularExpression() const;
    int filterKeyColumn() const;
    void setFilterKeyColumn(int column);
    Qt::CaseSensitivity filterCaseSensitivity() const;
    void setFilterCaseSensitivity(Qt::CaseSensitivity cs);

public slots:
    void invalidate();
    void setFilterFixedString(const QString &pattern);
    void setFilterRegularExpression(const QRegularExpression &regularExpression);
    void setFilterRegularExpression(const QString &pattern);
    void setFilterWildcard(const QString &pattern);

protected:
    virtual bool filterAcceptsRow(int source_row, const QModelIndex &source_parent) const;
    virtual bool filterAcceptsColumn(int source_column, const QModelIndex &source_parent) const;
    virtual bool lessThan(const QModelIndex &left, const QModelIndex &right) const;

public:
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &child) const;
    QObject *parent() const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    virtual bool hasChildren(const QModelIndex &parent = QModelIndex()) const;
    virtual QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const;
    virtual bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    virtual QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const;
    virtual bool setHeaderData(int section, Qt::Orientation orientation, const QVariant &value, int role = Qt::EditRole);
    virtual QMimeData *mimeData(const QModelIndexList &indexes) const /TransferBack/;
    virtual bool dropMimeData(const QMimeData *data, Qt::DropAction action, int row, int column, const QModelIndex &parent);
    virtual bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool insertColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual bool removeColumns(int column, int count, const QModelIndex &parent = QModelIndex());
    virtual void fetchMore(const QModelIndex &parent);
    virtual bool canFetchMore(const QModelIndex &parent) const;
    virtual Qt::ItemFlags flags(const QModelIndex &index) const;
    virtual QModelIndex buddy(const QModelIndex &index) const;
    virtual QSize span(const QModelIndex &index) const;
    virtual QModelIndexList match(const QModelIndex &start, int role, const QVariant &value, int hits = 1, Qt::MatchFlags flags = Qt::MatchFlags(Qt::MatchStartsWith | Qt::MatchWrap)) const;
    virtual void sort(int column, Qt::SortOrder order = Qt::AscendingOrder);
    Qt::CaseSensitivity sortCaseSensitivity() const;
    void setSortCaseSensitivity(Qt::CaseSensitivity cs);
    bool dynamicSortFilter() const;
    void setDynamicSortFilter(bool enable);
    int sortRole() const;
    void setSortRole(int role);
    int sortColumn() const;
    Qt::SortOrder sortOrder() const;
    int filterRole() const;
    void setFilterRole(int role);
    virtual QStringList mimeTypes() const;
    virtual Qt::DropActions supportedDropActions() const;
    bool isSortLocaleAware() const;
    void setSortLocaleAware(bool on);
    virtual QModelIndex sibling(int row, int column, const QModelIndex &idx) const;
    bool isRecursiveFilteringEnabled() const;
    void setRecursiveFilteringEnabled(bool recursive);

protected:
%If (Qt_6_9_0 -)
    void beginFilterChange();
%End
    void invalidateFilter();
    void invalidateRowsFilter();
    void invalidateColumnsFilter();

signals:
    void dynamicSortFilterChanged(bool dynamicSortFilter);
    void filterCaseSensitivityChanged(Qt::CaseSensitivity filterCaseSensitivity);
    void sortCaseSensitivityChanged(Qt::CaseSensitivity sortCaseSensitivity);
    void sortLocaleAwareChanged(bool sortLocaleAware);
    void sortRoleChanged(int sortRole);
    void filterRoleChanged(int filterRole);
    void recursiveFilteringEnabledChanged(bool recursiveFilteringEnabled);

public:
    bool autoAcceptChildRows() const;
    void setAutoAcceptChildRows(bool accept);

signals:
    void autoAcceptChildRowsChanged(bool autoAcceptChildRows);
};
