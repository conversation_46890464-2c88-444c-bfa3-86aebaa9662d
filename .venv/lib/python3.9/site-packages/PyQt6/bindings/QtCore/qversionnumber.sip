// qversionnumber.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVersionNumber
{
%TypeHeaderCode
#include <qversionnumber.h>
%End

public:
    QVersionNumber();
    explicit QVersionNumber(const QList<int> &seg);
    explicit QVersionNumber(int maj);
    QVersionNumber(int maj, int min);
    QVersionNumber(int maj, int min, int mic);
    bool isNull() const;
    bool isNormalized() const;
    int majorVersion() const;
    int minorVersion() const;
    int microVersion() const;
    QVersionNumber normalized() const;
    QList<int> segments() const;
%If (Qt_6_4_0 -)
    int segmentAt(qsizetype index) const;
%End
%If (- Qt_6_4_0)
    int segmentAt(int index) const;
%End
%If (Qt_6_4_0 -)
    qsizetype segmentCount() const;
%End
%If (- Qt_6_4_0)
    int segmentCount() const;
%End
    bool isPrefixOf(const QVersionNumber &other) const;
    static int compare(const QVersionNumber &v1, const QVersionNumber &v2);
    static QVersionNumber commonPrefix(const QVersionNumber &v1, const QVersionNumber &v2);
    QString toString() const;
%If (Qt_6_4_0 -)
    static QVersionNumber fromString(QAnyStringView string, qsizetype *suffixIndex = 0);
%End
%If (- Qt_6_4_0)
    static QVersionNumber fromString(const QString &string, int *suffixIndex = 0);
%End
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

bool operator>(const QVersionNumber &lhs, const QVersionNumber &rhs);
bool operator>=(const QVersionNumber &lhs, const QVersionNumber &rhs);
bool operator<(const QVersionNumber &lhs, const QVersionNumber &rhs);
bool operator<=(const QVersionNumber &lhs, const QVersionNumber &rhs);
bool operator==(const QVersionNumber &lhs, const QVersionNumber &rhs);
bool operator!=(const QVersionNumber &lhs, const QVersionNumber &rhs);
QDataStream &operator<<(QDataStream &out, const QVersionNumber &version) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QVersionNumber &version /Constrained/) /ReleaseGIL/;
%If (- Qt_6_7_0)

class QTypeRevision
{
%TypeHeaderCode
#include <qversionnumber.h>
%End

public:
    QTypeRevision();
    bool hasMajorVersion() const;
    quint8 majorVersion() const;
    bool hasMinorVersion() const;
    quint8 minorVersion() const;
    bool isValid() const;
    unsigned short toEncodedVersion() const;
%MethodCode
        sipRes = sipCpp->toEncodedVersion<unsigned short>();
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    static QTypeRevision fromEncodedVersion(int value);
    static QTypeRevision zero();
};

%End
%If (- Qt_6_7_0)
bool operator>(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
bool operator>=(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
bool operator<(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
bool operator<=(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
bool operator==(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
bool operator!=(QTypeRevision lhs, QTypeRevision rhs);
%End
%If (- Qt_6_7_0)
QDataStream &operator<<(QDataStream &out, const QTypeRevision &revision) /ReleaseGIL/;
%End
%If (- Qt_6_7_0)
QDataStream &operator>>(QDataStream &in, QTypeRevision &revision /Constrained/) /ReleaseGIL/;
%End
