// qabstractstate.sip generated by MetaSIP
//
// This file is part of the QtStateMachine Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAbstractState : public QObject
{
%TypeHeaderCode
#include <qabstractstate.h>
%End

public:
    virtual ~QAbstractState();
    QState *parentState() const;
    QStateMachine *machine() const;
    bool active() const;

signals:
    void activeChanged(bool active);
    void entered();
    void exited();

protected:
    QAbstractState(QState *parent /TransferThis/ = 0);
    virtual void onEntry(QEvent *event) = 0;
    virtual void onExit(QEvent *event) = 0;
    virtual bool event(QEvent *e);
};
