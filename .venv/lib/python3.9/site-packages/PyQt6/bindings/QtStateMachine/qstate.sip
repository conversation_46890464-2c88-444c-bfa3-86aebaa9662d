// qstate.sip generated by MetaSIP
//
// This file is part of the QtStateMachine Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QState : public QAbstractState
{
%TypeHeaderCode
#include <qstate.h>
%End

public:
    enum ChildMode
    {
        ExclusiveStates,
        ParallelStates,
    };

    enum RestorePolicy
    {
        DontRestoreProperties,
        RestoreProperties,
    };

    QState(QState *parent /TransferThis/ = 0);
    QState(QState::ChildMode childMode, QState *parent /TransferThis/ = 0);
    virtual ~QState();
    QAbstractState *errorState() const;
    void setErrorState(QAbstractState *state /KeepReference/);
    void addTransition(QAbstractTransition *transition /Transfer/);
    QSignalTransition *addTransition(SIP_PYOBJECT signal /TypeHint="pyqtBoundSignal"/, QAbstractState *target);
%MethodCode
        QObject *sender;
        QByteArray signal_signature;
        
        if ((sipError = pyqt6_qtstatemachine_get_pyqtsignal_parts(a0, &sender, signal_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addTransition(sender, signal_signature.constData(), a1);
        }
        else
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    QAbstractTransition *addTransition(QAbstractState *target /Transfer/);
    void removeTransition(QAbstractTransition *transition /TransferBack/);
    QList<QAbstractTransition *> transitions() const;
    QAbstractState *initialState() const;
    void setInitialState(QAbstractState *state /KeepReference/);
    QState::ChildMode childMode() const;
    void setChildMode(QState::ChildMode mode);
    void assignProperty(QObject *object, const char *name, const QVariant &value);

signals:
    void finished();
    void propertiesAssigned();
    void childModeChanged();
    void initialStateChanged();
    void errorStateChanged();

protected:
    virtual void onEntry(QEvent *event);
    virtual void onExit(QEvent *event);
    virtual bool event(QEvent *e);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt6_qtstatemachine_get_pyqtsignal_parts_t)(PyObject *, QObject **, QByteArray &);
extern pyqt6_qtstatemachine_get_pyqtsignal_parts_t pyqt6_qtstatemachine_get_pyqtsignal_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtstatemachine_get_pyqtsignal_parts_t pyqt6_qtstatemachine_get_pyqtsignal_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtstatemachine_get_pyqtsignal_parts = (pyqt6_qtstatemachine_get_pyqtsignal_parts_t)sipImportSymbol("pyqt6_get_pyqtsignal_parts");
Q_ASSERT(pyqt6_qtstatemachine_get_pyqtsignal_parts);
%End
