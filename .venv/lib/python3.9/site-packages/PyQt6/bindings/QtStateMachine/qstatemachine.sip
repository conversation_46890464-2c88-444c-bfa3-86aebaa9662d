// qstatemachine.sip generated by MetaSIP
//
// This file is part of the QtStateMachine Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStateMachine : public QState
{
%TypeHeaderCode
#include <qstatemachine.h>
%End

public:
    class SignalEvent : public QEvent /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qstatemachine.h>
%End

    public:
        virtual ~SignalEvent();
        QObject *sender() const;
        int signalIndex() const;
        QList<QVariant> arguments() const;
    };

    class WrappedEvent : public QEvent /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qstatemachine.h>
%End

    public:
        virtual ~WrappedEvent();
        QObject *object() const;
        QEvent *event() const;
    };

    enum EventPriority
    {
        NormalPriority,
        HighPriority,
    };

    enum Error
    {
        NoError,
        NoInitialStateError,
        NoDefaultStateInHistoryStateError,
        NoCommonAncestorForTransitionError,
        StateMachineChildModeSetToParallelError,
    };

    explicit QStateMachine(QObject *parent /TransferThis/ = 0);
    QStateMachine(QState::ChildMode childMode, QObject *parent /TransferThis/ = 0);
    virtual ~QStateMachine();
    void addState(QAbstractState *state /Transfer/);
    void removeState(QAbstractState *state /TransferBack/);
    QStateMachine::Error error() const;
    QString errorString() const;
    void clearError();
    bool isRunning() const;
    bool isAnimated() const;
    void setAnimated(bool enabled);
    void addDefaultAnimation(QAbstractAnimation *animation /GetWrapper/);
%MethodCode
        // We want to keep a reference to the animation but this is in addition to the
        // existing ones and does not replace them - so we can't use /KeepReference/.
        sipCpp->addDefaultAnimation(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (!user)
        {
            user = PyList_New(0);
            sipSetUserObject((sipSimpleWrapper *)sipSelf, user);
        }
        
        if (user)
            PyList_Append(user, a0Wrapper);
%End

    QList<QAbstractAnimation *> defaultAnimations() const;
    void removeDefaultAnimation(QAbstractAnimation *animation /GetWrapper/);
%MethodCode
        // Discard the extra animation reference that we took in addDefaultAnimation().
        sipCpp->removeDefaultAnimation(a0);
        
        // Use the user object as a list of the references.
        PyObject *user = sipGetUserObject((sipSimpleWrapper *)sipSelf);
        
        if (user)
        {
            Py_ssize_t i = 0;
        
            // Note that we deal with an object appearing in the list more than once.
            while (i < PyList_Size(user))
                if (PyList_GetItem(user, i) == a0Wrapper)
                    PyList_SetSlice(user, i, i + 1, NULL);
                else
                    ++i;
        }
%End

    QState::RestorePolicy globalRestorePolicy() const;
    void setGlobalRestorePolicy(QState::RestorePolicy restorePolicy);
    void postEvent(QEvent *event /Transfer/, QStateMachine::EventPriority priority = QStateMachine::NormalPriority);
    int postDelayedEvent(QEvent *event /Transfer/, int delay);
    bool cancelDelayedEvent(int id);
    QSet<QAbstractState *> configuration() const;
    virtual bool eventFilter(QObject *watched, QEvent *event);

public slots:
    void start();
    void stop();
    void setRunning(bool running);

signals:
    void started();
    void stopped();
    void runningChanged(bool running);

protected:
    virtual void onEntry(QEvent *event);
    virtual void onExit(QEvent *event);
    virtual bool event(QEvent *e);
};
