// qrgb.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qrgb.h>
%End

typedef unsigned int QRgb;
int qRed(QRgb rgb);
int qGreen(QRgb rgb);
int qBlue(QRgb rgb);
int qAlpha(QRgb rgb);
QRgb qRgb(int r, int g, int b);
QRgb qRgba(int r, int g, int b, int a);
int qGray(int r, int g, int b);
int qGray(QRgb rgb);
QRgb qPremultiply(QRgb x);
QRgb qUnpremultiply(QRgb p);
