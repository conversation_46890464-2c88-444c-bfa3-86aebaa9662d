// qpolygon.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPolygon
{
%TypeHeaderCode
#include <qpolygon.h>
%End

%PickleCode
    PyObject *pl = PyList_New(sipCpp->count() * 2);
    
    for (int p = 0, i = 0; i < sipCpp->count(); ++i, p += 2)
    {
        int x, y;
    
        sipCpp->point(i, &x, &y);
    
        PyList_SetItem(pl, p, PyLong_FromLong(x));
        PyList_SetItem(pl, p + 1, PyLong_FromLong(y));
    }
    
    sipRes = Py_BuildValue("(N)", pl);
%End

public:
    QPolygon();
    QPolygon(const QRect &r, bool closed = false);
    QPolygon(const QList<QPoint> &v);
    QPolygon(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QPolygon>())
            sipCpp = new QPolygon(a0->value<QPolygon>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    void swap(QPolygon &other /Constrained/);
    void translate(int dx, int dy);
    void translate(const QPoint &offset);
    QPolygon translated(int dx, int dy) const;
    QPolygon translated(const QPoint &offset) const;
    QRect boundingRect() const;
    QPoint point(int i) const;
    void setPoint(int index, int x, int y);
    void setPoint(int index, const QPoint &p);
    void setPoints(int firstx, int firsty, ... /TypeHint="int"/);
%MethodCode
        // Accept at least one pair of integer coordinates.
        int nPoints = 1 + ((PyTuple_Size(a2) + 1) >> 1);
        
        int *points = new int[nPoints * 2];
        
        points[0] = a0;
        points[1] = a1;
        
        for (Py_ssize_t i = 0; i < PyTuple_Size(a2); ++i)
            points[2 + i] = PyLong_AsLong(PyTuple_GetItem(a2, i));
        
        sipCpp->setPoints(nPoints, points);
        
        delete[] points;
%End

    void putPoints(int index, int firstx, int firsty, ... /TypeHint="int"/);
%MethodCode
        // Accept at least one pair of integer coordinates.
        int nPoints = 1 + ((PyTuple_Size(a3) + 1) >> 1);
        
        int *points = new int[nPoints * 2];
        
        points[0] = a1;
        points[1] = a2;
        
        for (Py_ssize_t i = 0; i < PyTuple_Size(a3); ++i)
            points[2 + i] = PyLong_AsLong(PyTuple_GetItem(a3, i));
        
        sipCpp->putPoints(a0, nPoints, points);
        
        delete[] points;
%End

    void putPoints(int index, int nPoints, const QPolygon &fromPolygon, int from = 0);
    bool containsPoint(const QPoint &pt, Qt::FillRule fillRule) const;
    QPolygon united(const QPolygon &r) const;
    QPolygon intersected(const QPolygon &r) const;
    QPolygon subtracted(const QPolygon &r) const;
    bool intersects(const QPolygon &r) const;
// Methods inherited from QList<QPoint> and Python special methods.
// Keep in sync with QPolygonF and QXmlStreamAttributes.

void append(const QPoint &value);
const QPoint &at(int i) const;
void clear();
bool contains(const QPoint &value) const;
int count(const QPoint &value) const;
int count() const /__len__/;
void *data();

// Note the Qt return value is discarded as it would require handwritten code
// and seems pretty useless.
void fill(const QPoint &value, int size = -1);

QPoint &first();
int indexOf(const QPoint &value, int from = 0) const;
void insert(int i, const QPoint &value);
bool isEmpty() const;
QPoint &last();
int lastIndexOf(const QPoint &value, int from = -1) const;

// Note the Qt return type is QList<QPoint>.
QPolygon mid(int pos, int length = -1) const;

void prepend(const QPoint &value);
void remove(int i);
void remove(int i, int count);
void replace(int i, const QPoint &value);
void resize(qsizetype size);
int size() const;
QPoint value(int i) const;
QPoint value(int i, const QPoint &defaultValue) const;
bool operator!=(const QPolygon &other) const;

// Note the Qt return type is QList<QPoint>.
QPolygon operator+(const QPolygon &other) const;

QPolygon &operator+=(const QPolygon &other);
QPolygon &operator+=(const QPoint &value);
bool operator==(const QPolygon &other) const;

SIP_PYOBJECT operator<<(const QPoint &value);
%MethodCode
    *a0 << *a1;

    sipRes = sipArg0;
    Py_INCREF(sipRes);
%End

QPoint &operator[](int i);
%MethodCode
Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());

if (idx < 0)
    sipIsErr = 1;
else
    sipRes = &sipCpp->operator[]((int)idx);
%End

// Some additional Python special methods.

void __setitem__(int i, const QPoint &value);
%MethodCode
int len;

len = sipCpp->count();

if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
    sipIsErr = 1;
else
    (*sipCpp)[a0] = *a1;
%End

void __setitem__(SIP_PYSLICE slice, const QPolygon &list);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    int vlen = a1->count();

    if (vlen != slicelength)
    {
        sipBadLengthForSlice(vlen, slicelength);
        sipIsErr = 1;
    }
    else
    {
        QList<QPoint>::const_iterator it = a1->begin();

        for (Py_ssize_t i = 0; i < slicelength; ++i)
        {
            (*sipCpp)[start] = *it;
            start += step;
            ++it;
        }
    }
}
%End

void __delitem__(int i);
%MethodCode
if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
    sipIsErr = 1;
else
    sipCpp->remove(a0);
%End

void __delitem__(SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        sipCpp->remove(start);
        start += step - 1;
    }
}
%End

QPolygon operator[](SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    sipRes = new QPolygon();

    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        (*sipRes) += (*sipCpp)[start];
        start += step;
    }
}
%End

int __contains__(const QPoint &value);
%MethodCode
sipRes = bool(sipCpp->contains(*a0));
%End
%If (Qt_6_4_0 -)
    QPolygonF toPolygonF() const;
%End
};

QDataStream &operator<<(QDataStream &stream, const QPolygon &polygon) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &stream, QPolygon &polygon /Constrained/) /ReleaseGIL/;

class QPolygonF
{
%TypeHeaderCode
#include <qpolygon.h>
%End

public:
    QPolygonF();
    QPolygonF(const QList<QPointF> &v);
    QPolygonF(const QRectF &r);
    QPolygonF(const QPolygon &a);
    QPolygonF(const QVariant &variant /GetWrapper/) /NoDerived/;
%MethodCode
        if (a0->canConvert<QPolygonF>())
            sipCpp = new QPolygonF(a0->value<QPolygonF>());
        else
            sipError = sipBadCallableArg(0, a0Wrapper);
%End

    void swap(QPolygonF &other /Constrained/);
    void translate(qreal dx, qreal dy);
    void translate(const QPointF &offset);
    QPolygonF translated(qreal dx, qreal dy) const;
    QPolygonF translated(const QPointF &offset) const;
    QPolygon toPolygon() const;
    bool isClosed() const;
    QRectF boundingRect() const;
    bool containsPoint(const QPointF &pt, Qt::FillRule fillRule) const;
    QPolygonF united(const QPolygonF &r) const;
    QPolygonF intersected(const QPolygonF &r) const;
    QPolygonF subtracted(const QPolygonF &r) const;
    bool intersects(const QPolygonF &r) const;
// Methods inherited from QList<QPointF> and Python special methods.
// Keep in sync with QPolygon and QXmlStreamAttributes.

void append(const QPointF &value);
const QPointF &at(int i) const;
void clear();
bool contains(const QPointF &value) const;
int count(const QPointF &value) const;
int count() const /__len__/;
void *data();

// Note the Qt return value is discarded as it would require handwritten code
// and seems pretty useless.
void fill(const QPointF &value, int size = -1);

QPointF &first();
int indexOf(const QPointF &value, int from = 0) const;
void insert(int i, const QPointF &value);
bool isEmpty() const;
QPointF &last();
int lastIndexOf(const QPointF &value, int from = -1) const;

// Note the Qt return type is QList<QPointF>.
QPolygonF mid(int pos, int length = -1) const;

void prepend(const QPointF &value);
void remove(int i);
void remove(int i, int count);
void replace(int i, const QPointF &value);
void resize(qsizetype size);
int size() const;
QPointF value(int i) const;
QPointF value(int i, const QPointF &defaultValue) const;
bool operator!=(const QPolygonF &other) const;

// Note the Qt return type is QList<QPointF>.
QPolygonF operator+(const QPolygonF &other) const;

QPolygonF &operator+=(const QPolygonF &other);
QPolygonF &operator+=(const QPointF &value);
bool operator==(const QPolygonF &other) const;

SIP_PYOBJECT operator<<(const QPointF &value);
%MethodCode
    *a0 << *a1;

    sipRes = sipArg0;
    Py_INCREF(sipRes);
%End

QPointF &operator[](int i);
%MethodCode
Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());

if (idx < 0)
    sipIsErr = 1;
else
    sipRes = &sipCpp->operator[]((int)idx);
%End

// Some additional Python special methods.

void __setitem__(int i, const QPointF &value);
%MethodCode
int len;

len = sipCpp->count();

if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
    sipIsErr = 1;
else
    (*sipCpp)[a0] = *a1;
%End

void __setitem__(SIP_PYSLICE slice, const QPolygonF &list);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    int vlen = a1->count();

    if (vlen != slicelength)
    {
        sipBadLengthForSlice(vlen, slicelength);
        sipIsErr = 1;
    }
    else
    {
        QList<QPointF>::const_iterator it = a1->begin();

        for (Py_ssize_t i = 0; i < slicelength; ++i)
        {
            (*sipCpp)[start] = *it;
            start += step;
            ++it;
        }
    }
}
%End

void __delitem__(int i);
%MethodCode
if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
    sipIsErr = 1;
else
    sipCpp->remove(a0);
%End

void __delitem__(SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        sipCpp->remove(start);
        start += step - 1;
    }
}
%End

QPolygonF operator[](SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    sipRes = new QPolygonF();

    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        (*sipRes) += (*sipCpp)[start];
        start += step;
    }
}
%End

int __contains__(const QPointF &value);
%MethodCode
sipRes = bool(sipCpp->contains(*a0));
%End
};

QDataStream &operator<<(QDataStream &stream, const QPolygonF &array) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &stream, QPolygonF &array /Constrained/) /ReleaseGIL/;
