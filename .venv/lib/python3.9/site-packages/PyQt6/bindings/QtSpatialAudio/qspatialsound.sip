// qspatialsound.sip generated by MetaSIP
//
// This file is part of the QtSpatialAudio Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)

class QSpatialSound : public QObject
{
%TypeHeaderCode
#include <qspatialsound.h>
%End

public:
    enum class DistanceModel
    {
        Logarithmic,
        Linear,
        ManualAttenuation,
    };

    enum Loops
    {
        Infinite,
        Once,
    };

    explicit QSpatialSound(QAudioEngine *engine);
    virtual ~QSpatialSound();
    void setSource(const QUrl &url);
    QUrl source() const;
    int loops() const;
    void setLoops(int loops);
    bool autoPlay() const;
    void setAutoPlay(bool autoPlay);
    void setPosition(QVector3D pos);
    QVector3D position() const;
    void setRotation(const QQuaternion &q);
    QQuaternion rotation() const;
    void setVolume(float volume);
    float volume() const;
    void setDistanceModel(QSpatialSound::DistanceModel model);
    QSpatialSound::DistanceModel distanceModel() const;
    void setSize(float size);
    float size() const;
    void setDistanceCutoff(float cutoff);
    float distanceCutoff() const;
    void setManualAttenuation(float attenuation);
    float manualAttenuation() const;
    void setOcclusionIntensity(float occlusion);
    float occlusionIntensity() const;
    void setDirectivity(float alpha);
    float directivity() const;
    void setDirectivityOrder(float alpha);
    float directivityOrder() const;
    void setNearFieldGain(float gain);
    float nearFieldGain() const;
    QAudioEngine *engine() const;

signals:
    void sourceChanged();
    void loopsChanged();
    void autoPlayChanged();
    void positionChanged();
    void rotationChanged();
    void volumeChanged();
    void distanceModelChanged();
    void sizeChanged();
    void distanceCutoffChanged();
    void manualAttenuationChanged();
    void occlusionIntensityChanged();
    void directivityChanged();
    void directivityOrderChanged();
    void nearFieldGainChanged();

public slots:
    void play();
    void pause();
    void stop();
};

%End
