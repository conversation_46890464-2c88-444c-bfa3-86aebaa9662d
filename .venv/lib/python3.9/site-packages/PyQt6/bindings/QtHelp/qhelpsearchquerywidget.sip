// qhelpsearchquerywidget.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpSearchQueryWidget : public QWidget
{
%TypeHeaderCode
#include <qhelpsearchquerywidget.h>
%End

public:
    explicit QHelpSearchQueryWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QHelpSearchQueryWidget();
    void expandExtendedSearch();
    void collapseExtendedSearch();

signals:
    void search();

private:
    virtual void focusInEvent(QFocusEvent *focusEvent);
    virtual void changeEvent(QEvent *event);

public:
    bool isCompactMode() const;
    void setCompactMode(bool on);
    QString searchInput() const;
    void setSearchInput(const QString &searchInput);
};
