// qquickview.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickView : public QQuickWindow /ExportDerived/
{
%TypeHeaderCode
#include <qquickview.h>
%End

public:
    explicit QQuickView(QWindow *parent /TransferThis/ = 0);
    QQuickView(QQmlEngine *engine, QWindow *parent /TransferThis/);
    QQuickView(const QUrl &source, QWindow *parent /TransferThis/ = 0);
%If (Qt_6_7_0 -)
    QQuickView(QAnyStringView uri, QAnyStringView typeName, QWindow *parent /TransferThis/ = 0);
%End
    virtual ~QQuickView() /ReleaseGIL/;
    QUrl source() const;
    QQmlEngine *engine() const;
    QQmlContext *rootContext() const;
    QQuickItem *rootObject() const;

    enum ResizeMode
    {
        SizeViewToRootObject,
        SizeRootObjectToView,
    };

    QQuickView::ResizeMode resizeMode() const;
    void setResizeMode(QQuickView::ResizeMode);

    enum Status
    {
        Null,
        Ready,
        Loading,
        Error,
    };

    QQuickView::Status status() const;
    QList<QQmlError> errors() const;
    QSize initialSize() const;

public slots:
    void setSource(const QUrl &) /ReleaseGIL/;
    void setInitialProperties(const QVariantMap &initialProperties);
%If (Qt_6_7_0 -)
    void loadFromModule(QAnyStringView uri, QAnyStringView typeName);
%End

signals:
    void statusChanged(QQuickView::Status);

protected:
    virtual void resizeEvent(QResizeEvent *);
    virtual void timerEvent(QTimerEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
};
