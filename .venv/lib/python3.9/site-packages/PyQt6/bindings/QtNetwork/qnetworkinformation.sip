// qnetworkinformation.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_1_0 -)

class QNetworkInformation : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qnetworkinformation.h>
%End

public:
    enum class Reachability
    {
        Unknown,
        Disconnected,
        Local,
        Site,
        Online,
    };

    enum class Feature
    {
        Reachability,
%If (Qt_6_2_0 -)
        CaptivePortal,
%End
%If (Qt_6_3_0 -)
        TransportMedium,
%End
%If (Qt_6_3_0 -)
        Metered,
%End
    };

    typedef QFlags<QNetworkInformation::Feature> Features;
    QNetworkInformation::Reachability reachability() const;
    QString backendName() const;
    bool supports(QNetworkInformation::Features features) const;
    static bool load(QStringView backend);
    static bool load(QNetworkInformation::Features features);
    static QStringList availableBackends();
    static QNetworkInformation *instance();

signals:
%If (Qt_6_8_0 -)
    void reachabilityChanged(QNetworkInformation::Reachability newReachability);
%End
%If (- Qt_6_8_0)
    void reachabilityChanged(QNetworkInformation::Reachability newReachability /ScopesStripped=1/);
%End

public:
%If (Qt_6_2_0 -)
    bool isBehindCaptivePortal() const;
%End

signals:
%If (Qt_6_2_0 -)
    void isBehindCaptivePortalChanged(bool state);
%End

public:
%If (Qt_6_3_0 -)

    enum class TransportMedium
    {
        Unknown,
        Ethernet,
        Cellular,
        WiFi,
        Bluetooth,
    };

%End
%If (Qt_6_3_0 -)
    QNetworkInformation::TransportMedium transportMedium() const;
%End
%If (Qt_6_3_0 -)
    bool isMetered() const;
%End
%If (Qt_6_3_0 -)
    QNetworkInformation::Features supportedFeatures() const;
%End
%If (Qt_6_3_0 -)
    static bool loadDefaultBackend();
%End

signals:
%If (Qt_6_8_0 -)
    void transportMediumChanged(QNetworkInformation::TransportMedium current);
%End
%If (Qt_6_3_0 - Qt_6_8_0)
    void transportMediumChanged(QNetworkInformation::TransportMedium current /ScopesStripped=1/);
%End
%If (Qt_6_3_0 -)
    void isMeteredChanged(bool isMetered);
%End

public:
%If (Qt_6_4_0 -)
    static bool loadBackendByName(QStringView backend);
%End
%If (Qt_6_4_0 -)
    static bool loadBackendByFeatures(QNetworkInformation::Features features);
%End

private:
    virtual ~QNetworkInformation();
};

%End
