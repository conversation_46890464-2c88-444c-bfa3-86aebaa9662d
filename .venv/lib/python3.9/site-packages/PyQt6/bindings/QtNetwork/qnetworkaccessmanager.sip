// qnetworkaccessmanager.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkAccessManager : public QObject
{
%TypeHeaderCode
#include <qnetworkaccessmanager.h>
%End

public:
    enum Operation
    {
        HeadOperation,
        GetOperation,
        PutOperation,
        PostOperation,
        DeleteOperation,
        CustomOperation,
    };

    explicit QNetworkAccessManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNetworkAccessManager();
    QNetworkProxy proxy() const;
    void setProxy(const QNetworkProxy &proxy);
    QNetworkCookieJar *cookieJar() const;
    void setCookieJar(QNetworkCookieJar *cookieJar /Transfer/);
    QNetworkReply *head(const QNetworkRequest &request) /Transfer/;
    QNetworkReply *get(const QNetworkRequest &request) /Transfer/;
%If (Qt_6_7_0 -)
    QNetworkReply *get(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
%End
%If (Qt_6_7_0 -)
    QNetworkReply *get(const QNetworkRequest &request, QIODevice *data) /Transfer/;
%End
    QNetworkReply *post(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *post(const QNetworkRequest &request, QHttpMultiPart *multiPart) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, QIODevice *data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, const QByteArray &data) /Transfer/;
    QNetworkReply *put(const QNetworkRequest &request, QHttpMultiPart *multiPart) /Transfer/;

signals:
    void proxyAuthenticationRequired(const QNetworkProxy &proxy, QAuthenticator *authenticator);
    void authenticationRequired(QNetworkReply *reply, QAuthenticator *authenticator);
    void finished(QNetworkReply *reply);
%If (PyQt_SSL)
    void encrypted(QNetworkReply *reply);
%End
%If (PyQt_SSL)
    void sslErrors(QNetworkReply *reply, const QList<QSslError> &errors);
%End
%If (PyQt_SSL)
    void preSharedKeyAuthenticationRequired(QNetworkReply *reply, QSslPreSharedKeyAuthenticator *authenticator);
%End

protected:
    virtual QNetworkReply *createRequest(QNetworkAccessManager::Operation op, const QNetworkRequest &request, QIODevice *device = 0) /AbortOnException,DisallowNone,ReleaseGIL/;

public:
    QNetworkProxyFactory *proxyFactory() const;
    void setProxyFactory(QNetworkProxyFactory *factory /Transfer/);
    QAbstractNetworkCache *cache() const;
    void setCache(QAbstractNetworkCache *cache /Transfer/);
    QNetworkReply *deleteResource(const QNetworkRequest &request) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, QIODevice *data = 0) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, const QByteArray &data) /Transfer/;
    QNetworkReply *sendCustomRequest(const QNetworkRequest &request, const QByteArray &verb, QHttpMultiPart *multiPart) /Transfer/;
    void clearAccessCache();
    virtual QStringList supportedSchemes() const;
%If (PyQt_SSL)
    void connectToHostEncrypted(const QString &hostName, quint16 port = 443, const QSslConfiguration &sslConfiguration = QSslConfiguration::defaultConfiguration());
%End
%If (PyQt_SSL)
    void connectToHostEncrypted(const QString &hostName, quint16 port, const QSslConfiguration &sslConfiguration, const QString &peerName);
%End
    void connectToHost(const QString &hostName, quint16 port = 80);

protected slots:
    QStringList supportedSchemesImplementation() const;

public:
    void clearConnectionCache();
    void setStrictTransportSecurityEnabled(bool enabled);
    bool isStrictTransportSecurityEnabled() const;
    void addStrictTransportSecurityHosts(const QList<QHstsPolicy> &knownHosts);
    QList<QHstsPolicy> strictTransportSecurityHosts() const;
    void setRedirectPolicy(QNetworkRequest::RedirectPolicy policy);
    QNetworkRequest::RedirectPolicy redirectPolicy() const;
    void enableStrictTransportSecurityStore(bool enabled, const QString &storeDir = QString());
    bool isStrictTransportSecurityStoreEnabled() const;
    bool autoDeleteReplies() const;
    void setAutoDeleteReplies(bool autoDelete);
    int transferTimeout() const;
    // In Qt v6.7 this was replaced by two overloads but we need the optional keyword argument.
    void setTransferTimeout(int timeout = QNetworkRequest::DefaultTransferTimeoutConstant);
};
