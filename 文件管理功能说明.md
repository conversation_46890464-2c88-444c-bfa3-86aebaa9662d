# 文件管理功能说明

## 功能概述

已成功为电玩管家管理系统添加了新的**文件管理**功能，位于左侧导航栏中。该功能包含两个主要模块：

### 1. 自选网站更新
- **路径**: `/www/wwwroot/zixuan`
- **功能**: 读取和显示服务器上自选网站相关的文件
- **显示内容**: 
  - 文件名
  - 文件大小
  - 最后修改时间
  - 文件/文件夹统计

### 2. 自选游戏
- **路径**: `/www/wwwroot/zixuan/submissions`
- **功能**: 读取和显示服务器上自选游戏相关的文件
- **显示内容**:
  - 游戏文件名
  - 文件大小
  - 最后修改时间
  - 文件/文件夹统计

## 技术实现

### 新增的代码组件

1. **导航按钮**: 在左侧导航栏添加了"文件管理"按钮
2. **页面创建**: `create_file_management_page()` 方法
3. **文件读取**: `refresh_zixuan_files()` 和 `refresh_submissions_files()` 方法
4. **文件大小格式化**: `format_file_size()` 方法

### 界面布局

```
文件管理页面
├── 标题: "文件管理"
├── 左侧区域: 自选网站更新
│   ├── 路径信息显示
│   ├── 刷新按钮
│   └── 文件列表显示区域
├── 右侧区域: 自选游戏
│   ├── 路径信息显示
│   ├── 刷新按钮
│   └── 文件列表显示区域
└── 底部状态栏
```

### 错误处理

- **路径不存在**: 显示友好的错误信息
- **权限不足**: 提示权限问题
- **读取异常**: 捕获并显示具体错误信息

### 开发环境适配

为了便于开发和测试，代码包含了路径回退机制：
- 优先尝试生产环境路径 (`/www/wwwroot/zixuan`)
- 如果不存在，则使用测试路径 (`test_zixuan`)

## 使用方法

1. 启动电玩管家管理系统
2. 点击左侧导航栏的"文件管理"
3. 在自选网站更新区域点击"刷新文件列表"查看网站文件
4. 在自选游戏区域点击"刷新文件列表"查看游戏文件
5. 查看文件详细信息（名称、大小、修改时间）

## 文件显示格式

```
📁 自选网站更新文件列表
📍 路径: /www/wwwroot/zixuan
==================================================
📄 website1.html (1.2 KB) - 2024-06-22 22:05:30
📄 website2.php (2.5 KB) - 2024-06-22 22:06:15
📁 images/
📁 css/
==================================================
📊 统计: 2 个文件夹, 2 个文件
```

## 测试文件

为了演示功能，项目中包含了以下测试文件：

- `demo_file_management.py`: 独立的文件管理功能演示程序
- `test_file_management.py`: 路径测试脚本
- `test_zixuan/`: 测试目录结构

## 部署注意事项

1. **服务器权限**: 确保应用程序有读取目标目录的权限
2. **路径存在**: 确认服务器上存在 `/www/wwwroot/zixuan` 和 `/www/wwwroot/zixuan/submissions` 目录
3. **文件编码**: 确保文件名支持中文显示

## 扩展功能建议

未来可以考虑添加的功能：
- 文件下载功能
- 文件上传功能
- 文件删除功能
- 文件搜索功能
- 文件预览功能
- 批量操作功能

## 代码修改总结

### 主要修改的文件
- `app.py`: 主应用程序文件

### 新增的方法
- `create_file_management_page()`: 创建文件管理页面
- `refresh_zixuan_files()`: 刷新自选网站文件列表
- `refresh_submissions_files()`: 刷新自选游戏文件列表
- `format_file_size()`: 格式化文件大小显示

### 修改的部分
- 导入语句: 添加了 `os` 模块和相关 PyQt6 组件
- 页面字典: 添加了 `"file_management"` 页面
- 导航按钮: 添加了"文件管理"按钮
- 页面切换逻辑: 在 `switch_page()` 方法中添加了文件管理页面处理

功能已完全实现并可以正常使用！
