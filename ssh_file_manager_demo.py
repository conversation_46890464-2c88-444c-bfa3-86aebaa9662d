#!/usr/bin/env python3
"""
SSH文件管理功能演示程序
"""

import sys
import os
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QGroupBox, QTableWidget, QTableWidgetItem, 
                             QHeaderView, QMessageBox, QProgressBar, QLineEdit, QFormLayout, QDialog)
from PyQt6.QtCore import Qt

# SSH相关导入
try:
    import paramiko
    SSH_AVAILABLE = True
except ImportError:
    SSH_AVAILABLE = False

class SSHTestDialog(QDialog):
    """SSH连接测试对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("SSH连接测试")
        self.setMinimumWidth(400)
        
        layout = QVBoxLayout(self)
        
        # 说明
        info_label = QLabel("""
SSH文件管理功能演示

此功能允许您：
• 连接到远程SSH服务器
• 浏览服务器文件系统
• 上传文件到服务器
• 从服务器下载文件
• 管理 /www/wwwroot/zixuan 目录
• 管理 /www/wwwroot/zixuan/submissions 目录

要使用此功能，您需要：
1. 服务器的SSH访问权限
2. 服务器IP地址和端口
3. 有效的用户名和密码
        """)
        info_label.setStyleSheet("background: #f0f0f0; padding: 15px; border: 1px solid #ccc; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 连接信息表单
        form_group = QGroupBox("连接信息示例")
        form_layout = QFormLayout(form_group)
        
        self.host_input = QLineEdit("your-server.com")
        self.host_input.setPlaceholderText("服务器IP或域名")
        form_layout.addRow("服务器地址:", self.host_input)
        
        self.port_input = QLineEdit("22")
        form_layout.addRow("端口:", self.port_input)
        
        self.username_input = QLineEdit("root")
        self.username_input.setPlaceholderText("SSH用户名")
        form_layout.addRow("用户名:", self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("SSH密码")
        form_layout.addRow("密码:", self.password_input)
        
        layout.addWidget(form_group)
        
        # 功能说明
        features_group = QGroupBox("主要功能")
        features_layout = QVBoxLayout(features_group)
        
        features_text = """
🔗 SSH连接管理
  • 安全的SSH连接
  • 连接状态显示
  • 自动重连机制

📁 文件浏览
  • 实时文件列表
  • 文件详细信息（大小、修改时间）
  • 目录导航

⬆️ 文件上传
  • 选择本地文件上传
  • 实时传输进度
  • 上传状态反馈

⬇️ 文件下载
  • 选择远程文件下载
  • 自定义保存位置
  • 下载进度显示

🎯 快速导航
  • 一键切换到自选网站更新目录
  • 一键切换到自选游戏目录
  • 路径显示和导航
        """
        
        features_label = QLabel(features_text)
        features_label.setStyleSheet("font-family: monospace; background: #f8f8f8; padding: 10px;")
        features_layout.addWidget(features_label)
        layout.addWidget(features_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        if SSH_AVAILABLE:
            test_btn = QPushButton("测试连接")
            test_btn.clicked.connect(self.test_connection)
            button_layout.addWidget(test_btn)
            
            status_label = QLabel("✅ paramiko库已安装，SSH功能可用")
            status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            install_btn = QPushButton("安装SSH库")
            install_btn.clicked.connect(self.install_paramiko)
            button_layout.addWidget(install_btn)
            
            status_label = QLabel("❌ 需要安装paramiko库")
            status_label.setStyleSheet("color: red; font-weight: bold;")
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        layout.addWidget(status_label)
    
    def test_connection(self):
        """测试SSH连接"""
        host = self.host_input.text().strip()
        port = self.port_input.text().strip()
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not all([host, port, username, password]):
            QMessageBox.warning(self, "错误", "请填写完整的连接信息")
            return
        
        try:
            port = int(port)
        except ValueError:
            QMessageBox.warning(self, "错误", "端口必须是数字")
            return
        
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(host, port=port, username=username, password=password, timeout=10)
            ssh.close()
            
            QMessageBox.information(self, "成功", "SSH连接测试成功！\n\n现在您可以在主程序中使用文件管理功能。")
            
        except Exception as e:
            QMessageBox.warning(self, "连接失败", f"SSH连接测试失败:\n{str(e)}\n\n请检查：\n• 服务器地址是否正确\n• 网络连接是否正常\n• 用户名密码是否正确\n• 服务器SSH服务是否开启")
    
    def install_paramiko(self):
        """安装paramiko库"""
        QMessageBox.information(self, "安装说明", 
                               "请在终端中运行以下命令安装SSH库：\n\n"
                               "pip install paramiko\n\n"
                               "安装完成后重启程序即可使用SSH功能。")

class SSHFileManagerDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SSH文件管理功能演示")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("SSH文件管理功能演示")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 功能介绍
        intro_text = """
这是电玩管家管理系统的SSH文件管理功能演示。

新功能包括：
• SSH服务器连接管理
• 远程文件浏览和管理
• 文件上传和下载
• 自选网站更新目录管理 (/www/wwwroot/zixuan)
• 自选游戏目录管理 (/www/wwwroot/zixuan/submissions)
        """
        
        intro_label = QLabel(intro_text)
        intro_label.setStyleSheet("background: #e8f4fd; padding: 20px; border: 1px solid #bee5eb; border-radius: 5px; font-size: 14px;")
        layout.addWidget(intro_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        demo_btn = QPushButton("查看功能说明")
        demo_btn.clicked.connect(self.show_demo)
        demo_btn.setStyleSheet("font-size: 16px; padding: 10px 20px;")
        button_layout.addWidget(demo_btn)
        
        main_btn = QPushButton("启动主程序")
        main_btn.clicked.connect(self.launch_main_app)
        main_btn.setStyleSheet("font-size: 16px; padding: 10px 20px; background: #007bff; color: white;")
        button_layout.addWidget(main_btn)
        
        layout.addLayout(button_layout)
        
        # 状态信息
        status_text = f"""
系统状态：
• Python版本: {sys.version.split()[0]}
• PyQt6: 已安装
• paramiko (SSH库): {'已安装' if SSH_AVAILABLE else '未安装'}
• 文件管理功能: {'可用' if SSH_AVAILABLE else '需要安装SSH库'}
        """
        
        status_label = QLabel(status_text)
        status_label.setStyleSheet("background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; font-family: monospace;")
        layout.addWidget(status_label)
        
        layout.addStretch()
    
    def show_demo(self):
        """显示功能演示"""
        dialog = SSHTestDialog(self)
        dialog.exec()
    
    def launch_main_app(self):
        """启动主程序"""
        try:
            from app import MainWindow
            self.main_window = MainWindow()
            self.main_window.show()
            # 直接切换到文件管理页面
            self.main_window.switch_page("file_management")
            QMessageBox.information(self, "成功", "主程序已启动，已切换到文件管理页面！")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"启动主程序失败:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    window = SSHFileManagerDemo()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
