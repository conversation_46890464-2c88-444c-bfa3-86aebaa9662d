#!/usr/bin/env python3
"""
验证新增功能
"""

import sys
import os

def verify_app_code():
    """验证app.py中的新功能代码"""
    print("🔍 验证app.py中的新功能...")
    
    if not os.path.exists('app.py'):
        print("❌ app.py文件不存在")
        return False
    
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查删除按钮相关代码
    checks = [
        ('删除按钮创建', 'delete_btn = QPushButton("删除选中文件")'),
        ('删除按钮样式', 'background-color: #dc3545'),
        ('删除方法', 'def delete_selected_file(self):'),
        ('文件存在检查', 'self.sftp_client.stat(remote_path)'),
        ('覆盖确认', '文件已存在'),
        ('删除确认', '此操作不可恢复'),
        ('选择变化信号', 'selectionChanged.connect'),
        ('状态更新方法', 'def on_file_selection_changed'),
    ]
    
    results = []
    for name, code in checks:
        if code in content:
            print(f"✅ {name}: 已实现")
            results.append(True)
        else:
            print(f"❌ {name}: 未找到")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n📊 功能实现率: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    return all(results)

def verify_imports():
    """验证导入是否正常"""
    print("\n🔍 验证导入...")
    
    try:
        # 测试PyQt6导入
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6导入成功")
        
        # 测试paramiko导入
        try:
            import paramiko
            print("✅ paramiko导入成功")
        except ImportError:
            print("⚠️ paramiko未安装，SSH功能不可用")
        
        # 测试主程序导入
        sys.path.append('.')
        from app import MainWindow
        print("✅ 主程序导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def create_simple_test():
    """创建简单的功能测试"""
    print("\n🔍 创建简单功能测试...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from app import MainWindow
        
        # 创建应用程序实例
        app = QApplication([])
        
        # 创建主窗口
        window = MainWindow()
        
        # 检查删除按钮是否存在
        if hasattr(window, 'delete_btn'):
            print("✅ 删除按钮存在")
            print(f"   按钮文本: {window.delete_btn.text()}")
            print(f"   按钮启用: {window.delete_btn.isEnabled()}")
            
            # 检查按钮样式
            style = window.delete_btn.styleSheet()
            if 'dc3545' in style or 'red' in style.lower():
                print("✅ 删除按钮样式正确（红色）")
            else:
                print("⚠️ 删除按钮样式可能不正确")
        else:
            print("❌ 删除按钮不存在")
        
        # 检查其他按钮
        buttons = ['upload_btn', 'download_btn', 'refresh_btn', 'connect_btn']
        for btn_name in buttons:
            if hasattr(window, btn_name):
                btn = getattr(window, btn_name)
                print(f"✅ {btn_name}: {btn.text()}")
            else:
                print(f"❌ {btn_name}: 不存在")
        
        # 检查文件表格
        if hasattr(window, 'files_table'):
            print("✅ 文件表格存在")
            
            # 检查选择信号连接
            selection_model = window.files_table.selectionModel()
            if hasattr(selection_model, 'selectionChanged'):
                receivers = selection_model.receivers(selection_model.selectionChanged)
                print(f"✅ 文件选择信号连接数: {receivers}")
            else:
                print("❌ 文件选择信号未连接")
        else:
            print("❌ 文件表格不存在")
        
        # 检查方法是否存在
        methods = ['delete_selected_file', 'upload_file', 'download_selected_file', 'on_file_selection_changed']
        for method_name in methods:
            if hasattr(window, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        app.quit()
        print("✅ 简单功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {str(e)}")
        return False

def main():
    print("🚀 SSH文件管理功能验证")
    print("=" * 50)
    
    # 验证代码
    code_ok = verify_app_code()
    
    # 验证导入
    import_ok = verify_imports()
    
    # 功能测试
    if import_ok:
        test_ok = create_simple_test()
    else:
        test_ok = False
    
    print("\n" + "=" * 50)
    print("📋 验证结果:")
    print(f"   代码检查: {'✅ 通过' if code_ok else '❌ 失败'}")
    print(f"   导入检查: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"   功能测试: {'✅ 通过' if test_ok else '❌ 失败'}")
    
    if all([code_ok, import_ok, test_ok]):
        print("\n🎉 所有功能验证通过！")
        print("\n📋 使用说明:")
        print("1. 运行: python app.py")
        print("2. 点击左侧'文件管理'")
        print("3. 点击'连接服务器'建立SSH连接")
        print("4. 连接成功后可以使用:")
        print("   • 🔄 刷新文件列表")
        print("   • ⬆️ 上传文件")
        print("   • ⬇️ 下载选中文件")
        print("   • 🗑️ 删除选中文件（红色按钮）")
        print("\n⚠️ 注意:")
        print("   • 删除操作不可恢复，请谨慎使用")
        print("   • 上传同名文件会提示是否覆盖")
    else:
        print("\n❌ 部分功能验证失败，请检查代码")
    
    return all([code_ok, import_ok, test_ok])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
