#!/usr/bin/env python3
"""
测试文件管理功能的简单脚本
"""

import os
import sys
from datetime import datetime

def test_file_management():
    """测试文件管理功能"""
    print("🔍 测试文件管理功能")
    print("=" * 50)
    
    # 测试路径
    zixuan_path = "/www/wwwroot/zixuan"
    submissions_path = "/www/wwwroot/zixuan/submissions"
    
    print(f"📁 测试路径1: {zixuan_path}")
    if os.path.exists(zixuan_path):
        print("✅ 路径存在")
        try:
            items = os.listdir(zixuan_path)
            print(f"📊 包含 {len(items)} 个项目")
            for item in items[:5]:  # 只显示前5个
                print(f"  - {item}")
            if len(items) > 5:
                print(f"  ... 还有 {len(items) - 5} 个项目")
        except PermissionError:
            print("❌ 权限不足")
        except Exception as e:
            print(f"❌ 错误: {e}")
    else:
        print("❌ 路径不存在")
    
    print()
    print(f"📁 测试路径2: {submissions_path}")
    if os.path.exists(submissions_path):
        print("✅ 路径存在")
        try:
            items = os.listdir(submissions_path)
            print(f"📊 包含 {len(items)} 个项目")
            for item in items[:5]:  # 只显示前5个
                print(f"  - {item}")
            if len(items) > 5:
                print(f"  ... 还有 {len(items) - 5} 个项目")
        except PermissionError:
            print("❌ 权限不足")
        except Exception as e:
            print(f"❌ 错误: {e}")
    else:
        print("❌ 路径不存在")
    
    print()
    print("💡 提示：如果路径不存在，这是正常的，因为这些是服务器上的路径。")
    print("💡 在实际服务器环境中，这些路径应该是可访问的。")

if __name__ == "__main__":
    test_file_management()
