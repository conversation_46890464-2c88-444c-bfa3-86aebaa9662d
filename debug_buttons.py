#!/usr/bin/env python3
"""
调试按钮点击问题
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QMessageBox)
from PyQt6.QtCore import Qt

class ButtonDebugTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮调试测试")
        self.setGeometry(100, 100, 500, 300)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("按钮点击调试测试")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("准备测试按钮点击")
        self.status_label.setStyleSheet("background: #f0f0f0; padding: 10px; border: 1px solid #ccc; border-radius: 5px;")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        button_layout = QVBoxLayout()
        
        # 测试普通按钮
        test_btn1 = QPushButton("测试按钮1 - 普通点击")
        test_btn1.clicked.connect(self.test_button1_clicked)
        button_layout.addWidget(test_btn1)
        
        # 测试带参数的按钮
        test_btn2 = QPushButton("测试按钮2 - 带参数")
        test_btn2.clicked.connect(lambda: self.test_button_with_param("参数测试"))
        button_layout.addWidget(test_btn2)
        
        # 测试启用/禁用状态
        self.test_btn3 = QPushButton("测试按钮3 - 启用/禁用")
        self.test_btn3.clicked.connect(self.test_button3_clicked)
        button_layout.addWidget(self.test_btn3)
        
        self.toggle_btn = QPushButton("切换按钮3状态")
        self.toggle_btn.clicked.connect(self.toggle_button3)
        button_layout.addWidget(self.toggle_btn)
        
        # 测试主程序按钮
        main_app_btn = QPushButton("测试主程序文件管理按钮")
        main_app_btn.clicked.connect(self.test_main_app_buttons)
        button_layout.addWidget(main_app_btn)
        
        layout.addLayout(button_layout)
        
        # 说明
        info_label = QLabel("""
测试说明：
1. 点击各个测试按钮，观察控制台输出
2. 检查按钮是否响应点击事件
3. 测试启用/禁用状态切换
4. 最后测试主程序的文件管理按钮
        """)
        info_label.setStyleSheet("background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 12px;")
        layout.addWidget(info_label)
    
    def test_button1_clicked(self):
        """测试按钮1点击"""
        print("DEBUG: 测试按钮1被点击")
        self.status_label.setText("✅ 测试按钮1点击成功")
        self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
        QMessageBox.information(self, "成功", "测试按钮1点击成功！")
    
    def test_button_with_param(self, param):
        """测试带参数的按钮点击"""
        print(f"DEBUG: 测试按钮2被点击，参数: {param}")
        self.status_label.setText(f"✅ 测试按钮2点击成功，参数: {param}")
        self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
        QMessageBox.information(self, "成功", f"测试按钮2点击成功！\n参数: {param}")
    
    def test_button3_clicked(self):
        """测试按钮3点击"""
        print("DEBUG: 测试按钮3被点击")
        if self.test_btn3.isEnabled():
            self.status_label.setText("✅ 测试按钮3点击成功（按钮已启用）")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            QMessageBox.information(self, "成功", "测试按钮3点击成功！\n按钮状态：已启用")
        else:
            self.status_label.setText("❌ 测试按钮3不应该被点击（按钮已禁用）")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")
    
    def toggle_button3(self):
        """切换按钮3的启用状态"""
        current_state = self.test_btn3.isEnabled()
        self.test_btn3.setEnabled(not current_state)
        new_state = "禁用" if current_state else "启用"
        print(f"DEBUG: 按钮3状态切换为: {new_state}")
        self.status_label.setText(f"按钮3状态已切换为: {new_state}")
        self.status_label.setStyleSheet("color: blue; background: #e6f3ff; padding: 10px; border: 1px solid blue; border-radius: 5px;")
    
    def test_main_app_buttons(self):
        """测试主程序的文件管理按钮"""
        try:
            print("DEBUG: 尝试导入主程序")
            from app import MainWindow
            
            print("DEBUG: 创建主程序实例")
            self.main_window = MainWindow()
            
            print("DEBUG: 切换到文件管理页面")
            self.main_window.switch_page("file_management")
            
            print("DEBUG: 显示主程序窗口")
            self.main_window.show()
            
            # 检查按钮状态
            if hasattr(self.main_window, 'upload_btn'):
                upload_enabled = self.main_window.upload_btn.isEnabled()
                print(f"DEBUG: 上传按钮状态: {'启用' if upload_enabled else '禁用'}")
            
            if hasattr(self.main_window, 'download_btn'):
                download_enabled = self.main_window.download_btn.isEnabled()
                print(f"DEBUG: 下载按钮状态: {'启用' if download_enabled else '禁用'}")
            
            self.status_label.setText("✅ 主程序已启动，请查看控制台输出")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            
            QMessageBox.information(self, "成功", 
                                   "主程序已启动！\n\n"
                                   "请按以下步骤测试：\n"
                                   "1. 查看控制台输出的按钮状态\n"
                                   "2. 尝试点击上传/下载按钮\n"
                                   "3. 观察控制台是否有DEBUG信息\n"
                                   "4. 如果按钮禁用，先连接SSH服务器")
            
        except Exception as e:
            print(f"DEBUG: 主程序启动失败: {str(e)}")
            self.status_label.setText(f"❌ 主程序启动失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")
            QMessageBox.warning(self, "错误", f"主程序启动失败:\n{str(e)}")

def main():
    app = QApplication(sys.argv)
    window = ButtonDebugTest()
    window.show()
    
    print("DEBUG: 按钮调试测试程序已启动")
    print("DEBUG: 请点击各个按钮测试功能")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
