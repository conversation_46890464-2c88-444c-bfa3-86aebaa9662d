import sys
import time
import uuid
import os
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout, QHBoxLayout, QWidget,
                             QPushButton, QFrame, QStackedWidget, QFormLayout, QLineEdit, QMessageBox,
                             QTableWidget, QTableWidgetItem, QHeaderView, QDialog, QSpinBox, QComboBox, QDateEdit,
                             QTextEdit, QScrollArea, QGroupBox)
from PyQt6.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# 导入我们自己的配置和数据库模块
from config import Config
from database import DatabaseManager

class RechargeDialog(QDialog):
    """充值对话框 - V3"""
    def __init__(self, user_id, user_login, db_manager, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.db_manager = db_manager

        self.setWindowTitle(f"为 {user_login} 修改信息")
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # --- 金币充值 ---
        coin_layout = QFormLayout()
        self.coin_spinbox = QSpinBox()
        self.coin_spinbox.setRange(-1000000, 1000000)
        self.coin_spinbox.setSuffix(" 金币")
        coin_layout.addRow("增加/减少金币:", self.coin_spinbox)
        layout.addLayout(coin_layout)

        # --- 分割线 ---
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)

        # --- 会员设置 ---
        vip_layout = QFormLayout()
        self.vip_action_combo = QComboBox()
        self.vip_action_combo.addItems(["不修改", "设为高级会员", "降为普通用户"])
        vip_layout.addRow("会员操作:", self.vip_action_combo)

        self.duration_combo = QComboBox()
        self.duration_combo.addItems(["包月 (30天)", "包年 (365天)", "永久会员", "自定义"])
        self.duration_label = QLabel("会员时长:")
        vip_layout.addRow(self.duration_label, self.duration_combo)

        self.expire_date_edit = QDateEdit(calendarPopup=True)
        self.expire_date_label = QLabel("到期时间:")
        vip_layout.addRow(self.expire_date_label, self.expire_date_edit)

        layout.addLayout(vip_layout)

        # --- 信号连接 ---
        self.vip_action_combo.currentTextChanged.connect(self.update_ui_state)
        self.duration_combo.currentTextChanged.connect(self.update_expiry_date)

        # --- 确认按钮 ---
        confirm_btn = QPushButton("确认修改")
        confirm_btn.clicked.connect(self.accept)
        layout.addWidget(confirm_btn)

        # --- 初始化UI状态 ---
        self.update_ui_state("不修改")

    def update_ui_state(self, action_text):
        """根据会员操作更新UI"""
        is_setting_vip = (action_text == "设为高级会员")
        self.duration_label.setVisible(is_setting_vip)
        self.duration_combo.setVisible(is_setting_vip)
        self.expire_date_label.setVisible(is_setting_vip)
        self.expire_date_edit.setVisible(is_setting_vip)
        if is_setting_vip:
            # 触发一次更新，确保初始值正确
            self.update_expiry_date(self.duration_combo.currentText())

    def update_expiry_date(self, duration_text):
        """根据时长选择更新日期"""
        self.expire_date_edit.setReadOnly(False) # 默认为可编辑
        if duration_text == "包月 (30天)":
            self.expire_date_edit.setDate(QDate.currentDate().addDays(30))
            self.expire_date_edit.setReadOnly(True)
        elif duration_text == "包年 (365天)":
            self.expire_date_edit.setDate(QDate.currentDate().addDays(365))
            self.expire_date_edit.setReadOnly(True)
        elif duration_text == "永久会员":
            self.expire_date_edit.setDate(QDate(9999, 9, 9))
            self.expire_date_edit.setReadOnly(True)
        elif duration_text == "自定义":
            self.expire_date_edit.setReadOnly(False)

    def get_recharge_data(self):
        """获取用户在对话框中输入的数据"""
        action = self.vip_action_combo.currentText()
        data = {
            "coins": self.coin_spinbox.value(),
            "vip_action": "no_change", # 默认
        }

        if action == "设为高级会员":
            data["vip_action"] = "set_vip"
            data["expire_date"] = self.expire_date_edit.date().toString("yyyy-MM-dd")
        elif action == "降为普通用户":
            data["vip_action"] = "set_normal"
            # 降为普通用户也需要一个过期日期，通常是过去的时间
            data["expire_date"] = "2020-01-01"
        
        return data

class GenerateCdkDialog(QDialog):
    """生成CDK对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("生成优惠码 (CDK)")
        self.setMinimumWidth(400)

        layout = QFormLayout(self)

        self.type_combo = QComboBox()
        self.type_map = {"充值卡": 1, "注册码": 2}
        self.type_combo.addItems(self.type_map.keys())
        layout.addRow("类型:", self.type_combo)

        self.amount_spinbox = QSpinBox()
        self.amount_spinbox.setRange(0, 100000)
        self.amount_spinbox.setSuffix(" 金币")
        self.amount_label = QLabel("金额:")
        layout.addRow(self.amount_label, self.amount_spinbox)

        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setRange(1, 1000)
        layout.addRow("数量:", self.quantity_spinbox)

        self.expiry_date_edit = QDateEdit(calendarPopup=True)
        self.expiry_date_edit.setDate(QDate.currentDate().addYears(1))
        self.expiry_date_edit.setMinimumDate(QDate.currentDate())
        layout.addRow("过期时间:", self.expiry_date_edit)

        self.type_combo.currentTextChanged.connect(self.update_ui)
        self.update_ui(self.type_combo.currentText())

        confirm_btn = QPushButton("确认生成")
        confirm_btn.clicked.connect(self.accept)
        layout.addRow(confirm_btn)

    def update_ui(self, type_text):
        is_recharge_card = (type_text == "充值卡")
        self.amount_label.setVisible(is_recharge_card)
        self.amount_spinbox.setVisible(is_recharge_card)

    def get_generation_data(self):
        type_text = self.type_combo.currentText()
        return {
            "type": self.type_map[type_text],
            "amount": self.amount_spinbox.value() if type_text == "充值卡" else 0,
            "quantity": self.quantity_spinbox.value(),
            "expiry_date": self.expiry_date_edit.date().toString("yyyy-MM-dd")
        }

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("电玩管家管理系统")
        self.setGeometry(100, 100, 1200, 800)

        # 用户管理状态
        self.current_page = 1
        self.users_per_page = 50
        self.sort_column = "u.ID"
        self.sort_order = "DESC"
        self.total_users = 0
        self.user_sort_map = {
            0: "u.ID",
            1: "u.user_login",
            2: "vip_level", # Allow sorting by computed column
            3: "balance",   # Allow sorting by computed column
            4: "u.user_email",
            6: "u.user_registered",
            7: "last_login" # Allow sorting by computed column
        }

        # 订单管理状态
        self.current_order_page = 1
        self.orders_per_page = 50
        self.order_sort_column = "o.id"
        self.order_sort_order = "DESC"
        self.total_orders = 0

        # CDK管理状态
        self.current_cdk_page = 1
        self.cdks_per_page = 50
        self.total_cdks = 0
        self.cdk_status_filter = "All" # All, Unused, Used

        # 下载记录状态
        self.current_download_page = 1
        self.downloads_per_page = 50
        self.total_downloads = 0

        # 文件管理状态
        self.file_management_data = {
            'zixuan_files': [],
            'submissions_files': []
        }

        # 初始化后端模块
        self.config = Config()
        self.db_manager = DatabaseManager()

        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # --- 左侧导航栏 ---
        nav_frame = QFrame()
        nav_frame.setFixedWidth(220)
        nav_frame.setStyleSheet("background-color: #f5f5f5; border-right: 1px solid #dcdcdc;")
        main_layout.addWidget(nav_frame)

        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        nav_layout.setContentsMargins(10, 10, 10, 10)
        nav_layout.setSpacing(10)

        title_label = QLabel("电玩管家管理系统")
        font = title_label.font(); font.setPointSize(18); font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #333333;")
        nav_layout.addWidget(title_label)
        nav_layout.addSpacing(20)

        # --- 右侧内容区 ---
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)
        
        # --- 创建页面 ---
        self.pages = {}
        self.pages["dashboard"] = self.create_dashboard_page()
        self.pages["users"] = self.create_users_page()
        self.pages["orders"] = self.create_orders_page()
        self.pages["cdk"] = self.create_cdk_page()
        self.pages["downloads"] = self.create_downloads_page()
        self.pages["file_management"] = self.create_file_management_page()
        self.pages["settings"] = self.create_settings_page()

        for page_widget in self.pages.values():
            self.stacked_widget.addWidget(page_widget)

        # --- 创建导航按钮 ---
        self.nav_buttons = {}
        self.create_nav_button("仪表板", "dashboard", nav_layout)
        self.create_nav_button("用户管理", "users", nav_layout)
        self.create_nav_button("订单管理", "orders", nav_layout)
        self.create_nav_button("优惠码管理", "cdk", nav_layout)
        self.create_nav_button("下载记录", "downloads", nav_layout)
        self.create_nav_button("文件管理", "file_management", nav_layout)
        self.create_nav_button("系统设置", "settings", nav_layout)

        # 默认显示仪表板
        self.nav_buttons["dashboard"].click()

        self._center_window()

    def create_nav_button(self, text, page_name, layout):
        button = QPushButton(text)
        button.setFixedHeight(40)
        button.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding-left: 20px;
                border: none;
                background-color: transparent;
                font-size: 14px;
                color: #333333;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                color: #333333;
            }
            QPushButton:checked {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
            }
        """)
        button.setCheckable(True)
        layout.addWidget(button)
        self.nav_buttons[page_name] = button
        button.clicked.connect(lambda: self.switch_page(page_name))

    def create_placeholder_page(self, text):
        page = QWidget()
        layout = QVBoxLayout(page)
        label = QLabel(f"{text} 页面\n\n(功能开发中)")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = label.font(); font.setPointSize(24); font.setBold(True); label.setFont(font)
        layout.addWidget(label)
        return page

    def create_dashboard_page(self):
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        title = QLabel("系统仪表盘")
        font = title.font(); font.setPointSize(24); font.setBold(True); title.setFont(font)
        layout.addWidget(title)

        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15)
        
        self.stat_cards = {}
        
        total_users_card = self.create_stat_card("总用户数", "0", "👥", "#3498db")
        self.stat_cards['total_users'] = total_users_card
        stats_layout.addWidget(total_users_card)
        
        today_users_card = self.create_stat_card("今日注册", "0", "📈", "#2ecc71")
        self.stat_cards['today_users'] = today_users_card
        stats_layout.addWidget(today_users_card)
        
        vip_users_card = self.create_stat_card("VIP用户", "0", "👑", "#f39c12")
        self.stat_cards['vip_users'] = vip_users_card
        stats_layout.addWidget(vip_users_card)
        
        total_balance_card = self.create_stat_card("总余额", "0", "💰", "#e74c3c")
        self.stat_cards['total_balance'] = total_balance_card
        stats_layout.addWidget(total_balance_card)
        
        layout.addLayout(stats_layout)

        quick_actions_layout = QHBoxLayout()
        quick_actions_layout.setSpacing(15)
        
        users_btn = self.create_quick_action_button("用户管理", "👥", lambda: self.switch_page("users"))
        recharge_btn = self.create_quick_action_button("订单管理", "💳", lambda: self.switch_page("orders"))
        settings_btn = self.create_quick_action_button("系统设置", "⚙️", lambda: self.switch_page("settings"))
        
        quick_actions_layout.addWidget(users_btn)
        quick_actions_layout.addWidget(recharge_btn)
        quick_actions_layout.addWidget(settings_btn)
        quick_actions_layout.addStretch()
        
        layout.addLayout(quick_actions_layout)

        recent_layout = QVBoxLayout()
        recent_title = QLabel("最近活动")
        font = recent_title.font(); font.setPointSize(18); font.setBold(True); recent_title.setFont(font)
        recent_layout.addWidget(recent_title)
        
        self.recent_activities_label = QLabel("正在加载...")
        self.recent_activities_label.setStyleSheet("color: #666; padding: 10px;")
        recent_layout.addWidget(self.recent_activities_label)
        
        layout.addLayout(recent_layout)
        layout.addStretch()

        self.load_dashboard_stats()
        
        return page

    def create_stat_card(self, title, value, icon, color):
        card = QFrame()
        card.setMinimumSize(220, 100)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }}
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)
        
        icon_bg = QFrame()
        icon_bg.setFixedSize(50, 50)
        icon_bg.setStyleSheet(f"""
            background-color: {color}30;
            border-radius: 25px;
        """)
        icon_layout = QVBoxLayout(icon_bg)
        icon_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color}; background-color: transparent; border: none;")
        icon_layout.addWidget(icon_label)
        
        layout.addWidget(icon_bg)
        layout.addSpacing(15)
        
        text_layout = QVBoxLayout()
        text_layout.setSpacing(0)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #212529; border: none;")
        text_layout.addWidget(value_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; color: #6c757d; border: none;")
        text_layout.addWidget(title_label)
        
        layout.addLayout(text_layout)
        
        card.value_label = value_label
        
        return card

    def create_quick_action_button(self, title, icon, callback):
        btn = QPushButton(f" {icon} {title}")
        btn.setFixedHeight(40)
        btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                color: #343a40;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-size: 14px;
                padding: 5px 15px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        btn.clicked.connect(callback)
        return btn

    def load_dashboard_stats(self):
        try:
            prefix = self.config.get('database', 'prefix', 'wp_')
            users_table = f"{prefix}users"
            usermeta_table = f"{prefix}usermeta"
            
            total_query = f"SELECT COUNT(*) as total FROM {users_table}"
            total_result, _ = self.db_manager.execute_query(total_query)
            total_users = total_result[0]['total'] if total_result else 0
            
            today_query = f"SELECT COUNT(*) as today FROM {users_table} WHERE DATE(user_registered) = CURDATE()"
            today_result, _ = self.db_manager.execute_query(today_query)
            today_users = today_result[0]['today'] if today_result else 0
            
            vip_query = f"SELECT COUNT(DISTINCT u.ID) as vip_count FROM {users_table} u JOIN {usermeta_table} um ON u.ID = um.user_id WHERE um.meta_key = 'ripro_level' AND um.meta_value != '普通用户' AND um.meta_value != ''"
            vip_result, _ = self.db_manager.execute_query(vip_query)
            vip_users = vip_result[0]['vip_count'] if vip_result else 0
            
            # V2 - 修正总余额统计，过滤非数字
            balance_query = f"""
                SELECT COALESCE(SUM(CAST(um.meta_value AS DECIMAL(10,2))), 0) as total_balance 
                FROM {usermeta_table} um 
                WHERE um.meta_key = 'cao_balance' AND um.meta_value REGEXP '^-?[0-9]+(\\\\.[0-9]+)?$'
            """
            balance_result, _ = self.db_manager.execute_query(balance_query)
            total_balance = balance_result[0]['total_balance'] if balance_result else 0
            
            if hasattr(self, 'stat_cards'):
                if 'total_users' in self.stat_cards: self.stat_cards['total_users'].value_label.setText(str(total_users))
                if 'today_users' in self.stat_cards: self.stat_cards['today_users'].value_label.setText(str(today_users))
                if 'vip_users' in self.stat_cards: self.stat_cards['vip_users'].value_label.setText(str(vip_users))
                if 'total_balance' in self.stat_cards: self.stat_cards['total_balance'].value_label.setText(f"{total_balance:.2f}")
            
            recent_query = f"SELECT u.user_login, u.user_registered FROM {users_table} u ORDER BY u.user_registered DESC LIMIT 5"
            recent_result, _ = self.db_manager.execute_query(recent_query)
            
            if recent_result:
                recent_text = "最近注册用户：\n"
                for user in recent_result:
                    reg_time = str(user['user_registered'])[:10]
                    recent_text += f"• {user['user_login']} ({reg_time})\n"
                self.recent_activities_label.setText(recent_text)
            else:
                self.recent_activities_label.setText("暂无最近活动")
                
        except Exception as e:
            self.recent_activities_label.setText(f"加载统计数据失败: {str(e)}")
    
    def create_users_page(self):
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        title = QLabel("用户管理")
        font = title.font(); font.setPointSize(22); font.setBold(True); title.setFont(font)
        layout.addWidget(title)

        controls_layout = QHBoxLayout()
        self.user_search_input = QLineEdit()
        self.user_search_input.setPlaceholderText("输入用户名、邮箱或手机号进行搜索...")
        self.user_search_input.textChanged.connect(self.on_search_text_changed)
        self.user_search_input.returnPressed.connect(self.search_users)
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_users)
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_users)
        
        controls_layout.addWidget(self.user_search_input)
        controls_layout.addWidget(search_btn)
        controls_layout.addWidget(refresh_btn)
        layout.addLayout(controls_layout)

        self.users_table = QTableWidget()
        self.users_table.setColumnCount(9)
        self.users_table.setHorizontalHeaderLabels([
            "ID", "用户", "会员等级", "余额", "邮箱", "手机号", "注册时间", "最后登录", "操作"
        ])
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.users_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(8, QHeaderView.ResizeMode.ResizeToContents)
        header.sectionClicked.connect(self.on_header_clicked)
        header.setSortIndicatorShown(True)
        
        layout.addWidget(self.users_table)

        pagination_layout = QHBoxLayout()
        self.prev_page_btn = QPushButton("上一页")
        self.prev_page_btn.clicked.connect(self.prev_page)
        self.page_label = QLabel("第 1 / 1 页")
        self.page_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.next_page_btn = QPushButton("下一页")
        self.next_page_btn.clicked.connect(self.next_page)

        pagination_layout.addStretch()
        pagination_layout.addWidget(self.prev_page_btn)
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addWidget(self.next_page_btn)
        pagination_layout.addStretch()

        layout.addLayout(pagination_layout)

        return page

    def create_orders_page(self):
        """创建订单管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        title = QLabel("订单管理")
        font = title.font(); font.setPointSize(24); font.setBold(True); title.setFont(font)
        layout.addWidget(title)
        
        # --- 搜索和刷新 ---
        controls_layout = QHBoxLayout()
        self.order_search_input = QLineEdit()
        self.order_search_input.setPlaceholderText("按订单号、用户、商品名搜索...")
        self.order_search_input.returnPressed.connect(self.refresh_orders)
        controls_layout.addWidget(self.order_search_input)
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_orders)
        controls_layout.addWidget(refresh_btn)
        layout.addLayout(controls_layout)

        # --- 订单表格 ---
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels(["ID", "订单号", "用户", "商品", "价格", "状态", "支付方式", "时间"])
        self.orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.orders_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)
        self.orders_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.orders_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        layout.addWidget(self.orders_table)

        # --- 分页控制 ---
        pagination_layout = QHBoxLayout()
        self.order_prev_page_btn = QPushButton("上一页")
        self.order_prev_page_btn.clicked.connect(self.prev_order_page)
        pagination_layout.addWidget(self.order_prev_page_btn)
        
        self.order_page_label = QLabel("第 1 / 1 页")
        pagination_layout.addWidget(self.order_page_label)
        
        self.order_next_page_btn = QPushButton("下一页")
        self.order_next_page_btn.clicked.connect(self.next_order_page)
        pagination_layout.addWidget(self.order_next_page_btn)
        pagination_layout.addStretch()
        layout.addLayout(pagination_layout)

        return page

    def create_cdk_page(self):
        """创建优惠码(CDK)管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        title = QLabel("优惠码 (CDK) 管理")
        font = title.font(); font.setPointSize(24); font.setBold(True); title.setFont(font)
        layout.addWidget(title)
        
        # --- 控制和生成 ---
        controls_layout = QHBoxLayout()
        
        generate_btn = QPushButton("生成CDK")
        generate_btn.clicked.connect(self.open_generate_cdk_dialog)
        controls_layout.addWidget(generate_btn)

        controls_layout.addStretch()

        filter_label = QLabel("状态筛选:")
        self.cdk_filter_combo = QComboBox()
        self.cdk_filter_combo.addItems(["全部", "未使用", "已使用"])
        self.cdk_filter_combo.currentTextChanged.connect(self.on_cdk_filter_changed)
        
        controls_layout.addWidget(filter_label)
        controls_layout.addWidget(self.cdk_filter_combo)

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_cdks)
        controls_layout.addWidget(refresh_btn)
        layout.addLayout(controls_layout)

        # --- CDK表格 ---
        self.cdk_table = QTableWidget()
        self.cdk_table.setColumnCount(6)
        self.cdk_table.setHorizontalHeaderLabels(["ID", "类型", "金额", "优惠码", "状态", "过期时间"])
        self.cdk_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.cdk_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.cdk_table)

        # --- 分页控制 ---
        pagination_layout = QHBoxLayout()
        self.cdk_prev_page_btn = QPushButton("上一页")
        self.cdk_prev_page_btn.clicked.connect(self.prev_cdk_page)
        pagination_layout.addWidget(self.cdk_prev_page_btn)
        
        self.cdk_page_label = QLabel("第 1 / 1 页")
        pagination_layout.addWidget(self.cdk_page_label)
        
        self.cdk_next_page_btn = QPushButton("下一页")
        self.cdk_next_page_btn.clicked.connect(self.next_cdk_page)
        pagination_layout.addWidget(self.cdk_next_page_btn)
        pagination_layout.addStretch()
        layout.addLayout(pagination_layout)

        return page

    def create_downloads_page(self):
        """创建下载记录页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)

        title = QLabel("下载记录")
        font = title.font(); font.setPointSize(24); font.setBold(True); title.setFont(font)
        layout.addWidget(title)
        
        # --- 控制 ---
        controls_layout = QHBoxLayout()
        controls_layout.addStretch()
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_downloads)
        controls_layout.addWidget(refresh_btn)
        layout.addLayout(controls_layout)

        # --- 下载记录表格 ---
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(5)
        self.downloads_table.setHorizontalHeaderLabels(["ID", "用户", "下载资源", "IP地址", "时间"])
        self.downloads_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.downloads_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.downloads_table)

        # --- 分页控制 ---
        pagination_layout = QHBoxLayout()
        self.download_prev_page_btn = QPushButton("上一页")
        self.download_prev_page_btn.clicked.connect(self.prev_download_page)
        pagination_layout.addWidget(self.download_prev_page_btn)
        
        self.download_page_label = QLabel("第 1 / 1 页")
        pagination_layout.addWidget(self.download_page_label)
        
        self.download_next_page_btn = QPushButton("下一页")
        self.download_next_page_btn.clicked.connect(self.next_download_page)
        pagination_layout.addWidget(self.download_next_page_btn)
        pagination_layout.addStretch()
        layout.addLayout(pagination_layout)

        return page

    def create_file_management_page(self):
        """创建文件管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        title = QLabel("文件管理")
        font = title.font(); font.setPointSize(24); font.setBold(True); title.setFont(font)
        layout.addWidget(title)

        # 创建两个主要区域
        main_content_layout = QHBoxLayout()
        main_content_layout.setSpacing(20)

        # 左侧：自选网站更新
        zixuan_group = QGroupBox("自选网站更新")
        zixuan_group.setMinimumWidth(400)
        zixuan_layout = QVBoxLayout(zixuan_group)

        zixuan_info_label = QLabel("路径: /www/wwwroot/zixuan")
        zixuan_info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        zixuan_layout.addWidget(zixuan_info_label)

        zixuan_refresh_btn = QPushButton("刷新文件列表")
        zixuan_refresh_btn.clicked.connect(self.refresh_zixuan_files)
        zixuan_layout.addWidget(zixuan_refresh_btn)

        self.zixuan_files_display = QTextEdit()
        self.zixuan_files_display.setReadOnly(True)
        self.zixuan_files_display.setMaximumHeight(400)
        self.zixuan_files_display.setPlaceholderText("点击刷新按钮加载文件列表...")
        zixuan_layout.addWidget(self.zixuan_files_display)

        main_content_layout.addWidget(zixuan_group)

        # 右侧：自选游戏
        submissions_group = QGroupBox("自选游戏")
        submissions_group.setMinimumWidth(400)
        submissions_layout = QVBoxLayout(submissions_group)

        submissions_info_label = QLabel("路径: /www/wwwroot/zixuan/submissions")
        submissions_info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        submissions_layout.addWidget(submissions_info_label)

        submissions_refresh_btn = QPushButton("刷新文件列表")
        submissions_refresh_btn.clicked.connect(self.refresh_submissions_files)
        submissions_layout.addWidget(submissions_refresh_btn)

        self.submissions_files_display = QTextEdit()
        self.submissions_files_display.setReadOnly(True)
        self.submissions_files_display.setMaximumHeight(400)
        self.submissions_files_display.setPlaceholderText("点击刷新按钮加载文件列表...")
        submissions_layout.addWidget(self.submissions_files_display)

        main_content_layout.addWidget(submissions_group)

        layout.addLayout(main_content_layout)

        # 底部状态信息
        status_layout = QHBoxLayout()
        self.file_status_label = QLabel("就绪")
        self.file_status_label.setStyleSheet("color: #666; font-size: 12px;")
        status_layout.addWidget(self.file_status_label)
        status_layout.addStretch()
        layout.addLayout(status_layout)

        layout.addStretch()
        return page

    def create_settings_page(self):
        page = QWidget()
        page_layout = QVBoxLayout(page)
        page_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        page_layout.setContentsMargins(25, 25, 25, 25)

        title = QLabel("系统设置")
        font = title.font(); font.setPointSize(22); font.setBold(True); title.setFont(font)
        page_layout.addWidget(title)
        page_layout.addSpacing(20)

        form_layout = QFormLayout()
        form_layout.setRowWrapPolicy(QFormLayout.RowWrapPolicy.WrapAllRows)
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        
        self.db_host_input = QLineEdit()
        self.db_port_input = QLineEdit()
        self.db_user_input = QLineEdit()
        self.db_pass_input = QLineEdit()
        self.db_pass_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.db_name_input = QLineEdit()
        self.db_prefix_input = QLineEdit()

        form_layout.addRow("数据库主机:", self.db_host_input)
        form_layout.addRow("端口:", self.db_port_input)
        form_layout.addRow("用户名:", self.db_user_input)
        form_layout.addRow("密码:", self.db_pass_input)
        form_layout.addRow("数据库名:", self.db_name_input)
        form_layout.addRow("表前缀:", self.db_prefix_input)

        page_layout.addLayout(form_layout)
        page_layout.addSpacing(20)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        test_btn = QPushButton("测试连接")
        save_btn = QPushButton("保存设置")
        test_btn.clicked.connect(self.test_db_connection)
        save_btn.clicked.connect(self.save_settings)
        
        button_layout.addWidget(test_btn)
        button_layout.addWidget(save_btn)
        page_layout.addLayout(button_layout)

        self.load_settings_to_ui()

        return page
        
    def load_settings_to_ui(self):
        db_config = self.config.get_section('database')
        self.db_host_input.setText(db_config.get('host', '127.0.0.1'))
        self.db_port_input.setText(db_config.get('port', '3306'))
        self.db_user_input.setText(db_config.get('user', ''))
        self.db_pass_input.setText(db_config.get('password', ''))
        self.db_name_input.setText(db_config.get('db', ''))
        self.db_prefix_input.setText(db_config.get('prefix', 'wp_'))

    def save_settings(self):
        self.config.set('database', 'host', self.db_host_input.text())
        self.config.set('database', 'port', self.db_port_input.text())
        self.config.set('database', 'user', self.db_user_input.text())
        self.config.set('database', 'password', self.db_pass_input.text())
        self.config.set('database', 'db', self.db_name_input.text())
        self.config.set('database', 'prefix', self.db_prefix_input.text())
        self.config.save()
        QMessageBox.information(self, "成功", "设置已成功保存！")

    def test_db_connection(self):
        self.config.set('database', 'host', self.db_host_input.text())
        self.config.set('database', 'port', self.db_port_input.text())
        self.config.set('database', 'user', self.db_user_input.text())
        self.config.set('database', 'password', self.db_pass_input.text())
        self.config.set('database', 'db', self.db_name_input.text())
        
        success, message = self.db_manager.test_connection()
        if success:
            QMessageBox.information(self, "连接成功", message)
        else:
            QMessageBox.warning(self, "连接失败", message)
    
    def on_search_text_changed(self):
        if not hasattr(self, 'search_timer'):
            self.search_timer = QTimer()
            self.search_timer.setSingleShot(True)
            self.search_timer.timeout.connect(self.search_users)
        self.search_timer.start(500)

    def search_users(self):
        if hasattr(self, 'search_timer') and self.search_timer.isActive():
            self.search_timer.stop()
        self.current_page = 1
        self.refresh_users()

    def refresh_users(self):
        search_term = self.user_search_input.text()
        prefix = self.config.get('database', 'prefix', 'wp_')
        users_table = f"{prefix}users"
        usermeta_table = f"{prefix}usermeta"

        count_params = []
        count_query_where = ""
        if search_term:
            count_query_where = " WHERE u.user_login LIKE %s OR u.user_email LIKE %s OR u.display_name LIKE %s"
            count_params.extend([f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"])
        
        count_query = f"SELECT COUNT(u.ID) as total FROM {users_table} u{count_query_where}"
        
        total_result, message = self.db_manager.execute_query(count_query, count_params)
        
        if total_result is None or not total_result:
             self.total_users = 0
        else:
            self.total_users = total_result[0]['total']

        # V2 - 更精准的SQL查询
        query = f"""
            SELECT 
                u.ID, u.user_login, u.display_name, u.user_email, u.user_registered,
                meta.cao_user_type,
                meta.ripro_level,
                meta.ripro_expiry,
                meta.cao_balance,
                meta.phone,
                meta.last_login
            FROM {users_table} u
            LEFT JOIN (
                SELECT
                    user_id,
                    MAX(CASE WHEN meta_key = 'cao_user_type' THEN meta_value END) as cao_user_type,
                    MAX(CASE WHEN meta_key = 'ripro_level' THEN meta_value END) as ripro_level,
                    MAX(CASE WHEN meta_key = 'ripro_expiry' THEN meta_value END) as ripro_expiry,
                    MAX(CASE WHEN meta_key = 'cao_balance' THEN meta_value END) as cao_balance,
                    MAX(CASE WHEN meta_key = 'phone' THEN meta_value END) as phone,
                    MAX(CASE WHEN meta_key = 'last_login' THEN meta_value END) as last_login
                FROM {usermeta_table}
                GROUP BY user_id
            ) AS meta ON u.ID = meta.user_id
        """
        params = []
        
        if search_term:
            query += f" WHERE (u.user_login LIKE %s OR u.user_email LIKE %s OR u.display_name LIKE %s)"
            params.extend([f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"])
        
        # 重新构建排序逻辑，因为部分列是计算出来的
        sort_column_sql = self.sort_column
        if sort_column_sql == 'vip_level':
            sort_column_sql = 'meta.cao_user_type' # 按实际身份排序
        elif sort_column_sql == 'balance':
            sort_column_sql = 'CAST(meta.cao_balance AS DECIMAL(10, 2))'
        elif sort_column_sql == 'last_login':
            sort_column_sql = 'CAST(meta.last_login AS UNSIGNED)'

        offset = (self.current_page - 1) * self.users_per_page
        query += f" ORDER BY {sort_column_sql} {self.sort_order} LIMIT %s OFFSET %s"
        params.extend([self.users_per_page, offset])

        users, message = self.db_manager.execute_query(query, params)

        if users is None:
            QMessageBox.warning(self, "查询失败", f"无法加载用户列表: {message}")
            self.users_table.setRowCount(0)
        else:
            self.users_table.setRowCount(0)
            for row_num, user_data in enumerate(users):
                self.users_table.insertRow(row_num)
                
                self.users_table.setItem(row_num, 0, QTableWidgetItem(str(user_data['ID'])))
                
                display_name = user_data.get('display_name') or user_data['user_login']
                self.users_table.setItem(row_num, 1, QTableWidgetItem(display_name))
                
                # 核心逻辑：基于 cao_user_type 判断
                is_vip = user_data.get('cao_user_type') == 'vip'
                vip_level_text = user_data.get('ripro_level') or '高级会员' if is_vip else '普通用户'
                vip_expire = user_data.get('ripro_expiry', '')

                vip_text = vip_level_text
                if is_vip and vip_expire:
                     try:
                        # 检查日期格式是否有效
                        datetime.strptime(vip_expire, '%Y-%m-%d')
                        vip_text += f" ({vip_expire})"
                     except (ValueError, TypeError):
                        pass # 日期格式错误则不显示

                self.users_table.setItem(row_num, 2, QTableWidgetItem(vip_text))
                
                balance = user_data.get('cao_balance', '0') or '0'
                self.users_table.setItem(row_num, 3, QTableWidgetItem(str(balance)))
                
                self.users_table.setItem(row_num, 4, QTableWidgetItem(user_data['user_email']))
                
                phone = user_data.get('phone', '')
                self.users_table.setItem(row_num, 5, QTableWidgetItem(phone))
                
                reg_time = str(user_data['user_registered'])[:10] if user_data['user_registered'] else ''
                self.users_table.setItem(row_num, 6, QTableWidgetItem(reg_time))
                
                last_login_ts = user_data.get('last_login')
                last_login_str = ''
                if last_login_ts:
                    try:
                        # last_login 可能是时间戳
                        last_login_str = datetime.fromtimestamp(int(last_login_ts)).strftime('%Y-%m-%d %H:%M')
                    except (ValueError, TypeError):
                        # 也可能是已经格式化的字符串
                        last_login_str = str(last_login_ts)
                self.users_table.setItem(row_num, 7, QTableWidgetItem(last_login_str))

                recharge_btn = QPushButton("修改")
                recharge_btn.clicked.connect(lambda checked, uid=user_data['ID'], uname=user_data['user_login']: self.open_recharge_dialog(uid, uname))
                self.users_table.setCellWidget(row_num, 8, recharge_btn)

        reverse_map = {v: k for k, v in self.user_sort_map.items()}
        col_index = reverse_map.get(self.sort_column, 0)
        sort_order_qt = Qt.SortOrder.AscendingOrder if self.sort_order == 'ASC' else Qt.SortOrder.DescendingOrder
        self.users_table.horizontalHeader().setSortIndicator(col_index, sort_order_qt)

        self.update_pagination_controls()

    def open_recharge_dialog(self, user_id, user_login):
        dialog = RechargeDialog(user_id, user_login, self.db_manager, self)
        if dialog.exec():
            data = dialog.get_recharge_data()
            self.perform_recharge(user_id, data)

    def perform_recharge(self, user_id, data):
        """执行实际的充值数据库操作 (V8 - 金币修复 + 拆分逻辑)"""
        prefix = self.config.get('database', 'prefix', 'wp_')
        
        # 1. 单独处理金币更新
        if data["coins"] != 0:
            self.update_user_balance(user_id, data["coins"])

        # 2. 更新会员信息
        vip_action = data.get("vip_action", "no_change")
        if vip_action != "no_change":
            self.update_user_vip_status(user_id, vip_action, data.get('expire_date'))
        
        QMessageBox.information(self, "成功", "操作已成功执行！")
        self.refresh_users()
        
    def update_user_balance(self, user_id, amount_change):
        """ V10 - 最终修复版：采用读取-计算-写入的绝对可靠策略 """
        if amount_change == 0:
            return True

        prefix = self.config.get('database', 'prefix', 'wp_')
        usermeta_table = f"{prefix}usermeta"
        
        # 1. 查询当前余额
        get_balance_query = f"SELECT meta_value FROM {usermeta_table} WHERE user_id = %s AND meta_key = 'cao_balance'"
        result, msg = self.db_manager.execute_query(get_balance_query, (user_id,))
        
        if result is None:
            QMessageBox.warning(self, "查询余额失败", f"数据库错误: {msg}")
            return False
            
        current_balance = 0.0
        if result:
            try:
                current_balance = float(result[0]['meta_value'])
            except (ValueError, TypeError, IndexError):
                current_balance = 0.0
                
        # 2. 计算新余额
        new_balance = current_balance + float(amount_change)
        
        # 3. 写回新余额
        # 首先尝试更新。这是最常见的情况。
        update_query = f"UPDATE {usermeta_table} SET meta_value = %s WHERE user_id = %s AND meta_key = 'cao_balance'"
        rows_updated, msg_update = self.db_manager.execute_update(update_query, (f"{new_balance:.2f}", user_id))
        
        if rows_updated is None:
            QMessageBox.warning(self, "更新余额失败", f"数据库错误: {msg_update}")
            return False
            
        # 如果没有行被更新，说明之前没有这个记录，现在插入它。
        if rows_updated == 0:
            insert_query = f"INSERT INTO {usermeta_table} (user_id, meta_key, meta_value) VALUES (%s, %s, %s)"
            rows_inserted, msg_insert = self.db_manager.execute_update(insert_query, (user_id, 'cao_balance', f"{new_balance:.2f}"))
            
            if rows_inserted is None:
                QMessageBox.warning(self, "插入余额记录失败", f"数据库错误: {msg_insert}")
                return False
                
        return True

    def update_user_vip_status(self, user_id, action, expire_date=None):
        """ 专用于更新用户VIP状态的函数 """
        prefix = self.config.get('database', 'prefix', 'wp_')
        usermeta_table = f"{prefix}usermeta"

        def update_meta(key, value):
            update_query = f"UPDATE {usermeta_table} SET meta_value = %s WHERE user_id = %s AND meta_key = %s"
            rows_updated, msg_update = self.db_manager.execute_update(update_query, (value, user_id, key))
            if rows_updated is None: return False
            if rows_updated > 0: return True
            insert_query = f"INSERT INTO {usermeta_table} (user_id, meta_key, meta_value) VALUES (%s, %s, %s)"
            rows_inserted, msg_insert = self.db_manager.execute_update(insert_query, (user_id, key, value))
            return rows_inserted is not None

        if action == "set_vip" and expire_date:
            update_meta('ripro_level', '高级会员')
            update_meta('cao_user_type', 'vip')
            update_meta('ripro_expiry', expire_date)
            update_meta('cao_vip_end_time', expire_date)
        
        elif action == "set_normal":
            update_meta('ripro_level', '普通用户')
            update_meta('cao_user_type', 'normal')
            if expire_date:
                update_meta('ripro_expiry', expire_date)
                update_meta('cao_vip_end_time', expire_date)
        
    def _is_current_user_vip(self, user_id):
        prefix = self.config.get('database', 'prefix', 'wp_')
        usermeta_table = f"{prefix}usermeta"
        query = f"SELECT meta_value FROM {usermeta_table} WHERE user_id = %s AND meta_key = 'ripro_level'"
        result, _ = self.db_manager.execute_query(query, (user_id,))
        if result and result[0]['meta_value'] not in ['普通用户', '', None]:
            return True
        return False
    
    def prev_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            self.refresh_users()

    def next_page(self):
        total_pages = (self.total_users + self.users_per_page - 1) // self.users_per_page
        if self.current_page < total_pages:
            self.current_page += 1
            self.refresh_users()

    def on_header_clicked(self, logical_index):
        if logical_index in self.user_sort_map:
            new_sort_column = self.user_sort_map[logical_index]
            if self.sort_column == new_sort_column:
                self.sort_order = "ASC" if self.sort_order == "DESC" else "DESC"
            else:
                self.sort_column = new_sort_column
                self.sort_order = "DESC"
            
            self.current_page = 1
            self.refresh_users()

    def update_pagination_controls(self):
        self.prev_page_btn.setEnabled(self.current_page > 1)
        
        total_pages = (self.total_users + self.users_per_page - 1) // self.users_per_page
        if total_pages == 0: total_pages = 1
        
        self.next_page_btn.setEnabled(self.current_page < total_pages)
        self.page_label.setText(f"第 {self.current_page} / {total_pages} 页 (共 {self.total_users} 条)")

    def switch_page(self, page_name):
        self.stacked_widget.setCurrentWidget(self.pages[page_name])
        for name, button in self.nav_buttons.items():
            button.setChecked(name == page_name)
        
        if page_name == "users":
            self.current_page = 1
            self.refresh_users()
        elif page_name == "orders":
            self.current_order_page = 1
            self.refresh_orders()
        elif page_name == "cdk":
            self.current_cdk_page = 1
            self.refresh_cdks()
        elif page_name == "downloads":
            self.current_download_page = 1
            self.refresh_downloads()
        elif page_name == "file_management":
            # 文件管理页面不需要特殊初始化
            pass
        elif page_name == "dashboard":
            self.load_dashboard_stats()

    def _center_window(self):
        screen_geometry = self.screen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2
        self.move(x, y)

    def refresh_orders(self):
        """刷新订单列表 - V2 (使用正确表结构)"""
        prefix = self.config.get('database', 'prefix', 'wp_')
        order_table = f"{prefix}cao_order_tbl" # V2: 使用正确的表名
        users_table = f"{prefix}users"
        posts_table = f"{prefix}posts"

        search_term = self.order_search_input.text()
        
        # 获取总数
        count_query = f"SELECT COUNT(o.id) as total FROM {order_table} o"
        count_params = []
        if search_term:
            # V2: 更新搜索的字段
            count_query += f" LEFT JOIN {users_table} u ON o.user_id = u.ID LEFT JOIN {posts_table} p ON o.post_id = p.ID WHERE o.order_trade_no LIKE %s OR u.user_login LIKE %s OR p.post_title LIKE %s"
            term = f"%{search_term}%"
            count_params.extend([term, term, term])

        total_result, _ = self.db_manager.execute_query(count_query, count_params)
        self.total_orders = total_result[0]['total'] if total_result else 0

        # 获取当前页数据
        # V2: 更新查询的字段
        query = f"""
            SELECT o.id, o.order_trade_no, o.user_id, o.post_id, o.order_price, o.pay_status, o.pay_type, o.create_time, o.order_type,
                   u.user_login,
                   p.post_title
            FROM {order_table} o
            LEFT JOIN {users_table} u ON o.user_id = u.ID
            LEFT JOIN {posts_table} p ON o.post_id = p.ID
        """
        params = []
        if search_term:
            # V2: 更新搜索的字段
            query += " WHERE o.order_trade_no LIKE %s OR u.user_login LIKE %s OR p.post_title LIKE %s"
            term = f"%{search_term}%"
            params.extend([term, term, term])

        offset = (self.current_order_page - 1) * self.orders_per_page
        query += f" ORDER BY {self.order_sort_column} {self.order_sort_order} LIMIT %s OFFSET %s"
        params.extend([self.orders_per_page, offset])

        orders, message = self.db_manager.execute_query(query, params)

        self.orders_table.setRowCount(0)
        if orders is None:
            QMessageBox.warning(self, "查询失败", f"无法加载订单列表: {message}")
            return
            
        for row, order_data in enumerate(orders):
            self.orders_table.insertRow(row)
            self.orders_table.setItem(row, 0, QTableWidgetItem(str(order_data['id'])))
            self.orders_table.setItem(row, 1, QTableWidgetItem(order_data.get('order_trade_no', ''))) # V2: order_trade_no
            self.orders_table.setItem(row, 2, QTableWidgetItem(order_data.get('user_login', 'N/A')))
            
            # V2: 智能显示商品名称
            order_type = order_data.get('order_type')
            post_title = order_data.get('post_title')
            product_name = 'N/A'
            if order_type == 1 and post_title:
                product_name = post_title
            elif order_type == 2:
                product_name = '金币充值'
            elif order_type == 3:
                product_name = '会员购买'
            elif order_type == 4:
                product_name = '其他'
            self.orders_table.setItem(row, 3, QTableWidgetItem(product_name))
            
            self.orders_table.setItem(row, 4, QTableWidgetItem(str(order_data.get('order_price', '0'))))
            
            # V2: pay_status
            status_map = {0: "未支付", 1: "已支付"}
            status_text = status_map.get(order_data.get('pay_status'), "未知")
            self.orders_table.setItem(row, 5, QTableWidgetItem(status_text))
            
            # V3: 支付方式人性化显示
            pay_type_map = {
                'alipay': '支付宝',
                'wxpay': '微信支付',
                'balance': '余额支付'
            }
            pay_type_key = order_data.get('pay_type', '')
            pay_type_text = pay_type_map.get(pay_type_key, pay_type_key or 'N/A')
            self.orders_table.setItem(row, 6, QTableWidgetItem(pay_type_text))
            
            # V2: 格式化时间戳
            create_ts = order_data.get('create_time')
            create_time_str = ''
            if create_ts:
                try:
                    create_time_str = datetime.fromtimestamp(int(create_ts)).strftime('%Y-%m-%d %H:%M')
                except (ValueError, TypeError):
                    create_time_str = str(create_ts)
            self.orders_table.setItem(row, 7, QTableWidgetItem(create_time_str))

        self.update_order_pagination_controls()

    def update_order_pagination_controls(self):
        self.order_prev_page_btn.setEnabled(self.current_order_page > 1)
        total_pages = (self.total_orders + self.orders_per_page - 1) // self.orders_per_page or 1
        self.order_next_page_btn.setEnabled(self.current_order_page < total_pages)
        self.order_page_label.setText(f"第 {self.current_order_page} / {total_pages} 页 (共 {self.total_orders} 条)")

    def prev_order_page(self):
        if self.current_order_page > 1:
            self.current_order_page -= 1
            self.refresh_orders()

    def next_order_page(self):
        total_pages = (self.total_orders + self.orders_per_page - 1) // self.orders_per_page
        if self.current_order_page < total_pages:
            self.current_order_page += 1
            self.refresh_orders()

    def refresh_cdks(self):
        """刷新CDK列表"""
        prefix = self.config.get('database', 'prefix', 'wp_')
        cdk_table = f"{prefix}cao_cdk_tbl"
        
        where_clause = ""
        params = []
        if self.cdk_status_filter == "Unused":
            where_clause = " WHERE status = 0"
        elif self.cdk_status_filter == "Used":
            where_clause = " WHERE status = 1"

        count_query = f"SELECT COUNT(*) as total FROM {cdk_table}{where_clause}"
        total_result, _ = self.db_manager.execute_query(count_query)
        self.total_cdks = total_result[0]['total'] if total_result else 0
        
        query = f"SELECT * FROM {cdk_table}{where_clause} ORDER BY id DESC LIMIT %s OFFSET %s"
        offset = (self.current_cdk_page - 1) * self.cdks_per_page
        cdks, message = self.db_manager.execute_query(query, [self.cdks_per_page, offset])

        self.cdk_table.setRowCount(0)
        if cdks is None:
            QMessageBox.warning(self, "查询失败", f"无法加载CDK列表: {message}")
            return

        for row, cdk_data in enumerate(cdks):
            self.cdk_table.insertRow(row)
            self.cdk_table.setItem(row, 0, QTableWidgetItem(str(cdk_data['id'])))
            
            type_map = {1: '充值卡', 2: '注册码'}
            self.cdk_table.setItem(row, 1, QTableWidgetItem(type_map.get(cdk_data['type'], '未知')))
            
            self.cdk_table.setItem(row, 2, QTableWidgetItem(str(cdk_data.get('amount', ''))))
            self.cdk_table.setItem(row, 3, QTableWidgetItem(cdk_data.get('code', '')))
            
            status_map = {0: '未使用', 1: '已使用'}
            self.cdk_table.setItem(row, 4, QTableWidgetItem(status_map.get(cdk_data['status'], '未知')))

            expiry_ts = cdk_data.get('expiry_time')
            expiry_str = 'N/A'
            if expiry_ts:
                try:
                    expiry_str = datetime.fromtimestamp(int(expiry_ts)).strftime('%Y-%m-%d %H:%M')
                except: pass
            self.cdk_table.setItem(row, 5, QTableWidgetItem(expiry_str))

        self.update_cdk_pagination_controls()

    def update_cdk_pagination_controls(self):
        self.cdk_prev_page_btn.setEnabled(self.current_cdk_page > 1)
        total_pages = (self.total_cdks + self.cdks_per_page - 1) // self.cdks_per_page or 1
        self.cdk_next_page_btn.setEnabled(self.current_cdk_page < total_pages)
        self.cdk_page_label.setText(f"第 {self.current_cdk_page} / {total_pages} 页 (共 {self.total_cdks} 条)")

    def prev_cdk_page(self):
        if self.current_cdk_page > 1:
            self.current_cdk_page -= 1
            self.refresh_cdks()

    def next_cdk_page(self):
        total_pages = (self.total_cdks + self.cdks_per_page - 1) // self.cdks_per_page
        if self.current_cdk_page < total_pages:
            self.current_cdk_page += 1
            self.refresh_cdks()

    def on_cdk_filter_changed(self, filter_text):
        """当CDK状态筛选变化时调用"""
        self.cdk_status_filter = {"全部": "All", "未使用": "Unused", "已使用": "Used"}.get(filter_text, "All")
        self.current_cdk_page = 1
        self.refresh_cdks()
        
    def open_generate_cdk_dialog(self):
        """打开生成CDK的对话框"""
        dialog = GenerateCdkDialog(self)
        if dialog.exec():
            data = dialog.get_generation_data()
            self.perform_cdk_generation(data)

    def perform_cdk_generation(self, data):
        """执行CDK的生成"""
        prefix = self.config.get('database', 'prefix', 'wp_')
        cdk_table = f"{prefix}cao_cdk_tbl"
        
        quantity = data['quantity']
        expiry_datetime = datetime.strptime(data['expiry_date'], "%Y-%m-%d")
        expiry_timestamp = int(time.mktime(expiry_datetime.timetuple()))

        new_codes = []
        for _ in range(quantity):
            code = uuid.uuid4().hex.upper()[:16]
            new_codes.append((
                data['type'],
                data['amount'],
                int(time.time()),
                expiry_timestamp,
                code,
                0 # status: unused
            ))

        query = f"INSERT INTO {cdk_table} (type, amount, create_time, expiry_time, code, status) VALUES (%s, %s, %s, %s, %s, %s)"
        
        rows_inserted, message = self.db_manager.execute_many(query, new_codes)

        if rows_inserted is not None:
            QMessageBox.information(self, "成功", f"已成功生成 {rows_inserted} 个优惠码！")
            self.refresh_cdks()
        else:
            QMessageBox.warning(self, "生成失败", f"数据库错误: {message}")

    def refresh_downloads(self):
        """刷新下载记录列表"""
        prefix = self.config.get('database', 'prefix', 'wp_')
        downloads_table = f"{prefix}cao_down_tbl"
        users_table = f"{prefix}users"
        posts_table = f"{prefix}posts"
        
        count_query = f"SELECT COUNT(*) as total FROM {downloads_table}"
        total_result, _ = self.db_manager.execute_query(count_query)
        self.total_downloads = total_result[0]['total'] if total_result else 0
        
        query = f"""
            SELECT d.id, d.user_id, d.post_id, d.create_time, d.ip,
                   u.user_login,
                   p.post_title
            FROM {downloads_table} d
            LEFT JOIN {users_table} u ON d.user_id = u.ID
            LEFT JOIN {posts_table} p ON d.post_id = p.ID
            ORDER BY d.id DESC LIMIT %s OFFSET %s
        """
        offset = (self.current_download_page - 1) * self.downloads_per_page
        downloads, message = self.db_manager.execute_query(query, [self.downloads_per_page, offset])

        self.downloads_table.setRowCount(0)
        if downloads is None:
            QMessageBox.warning(self, "查询失败", f"无法加载下载记录: {message}")
            return

        for row, data in enumerate(downloads):
            self.downloads_table.insertRow(row)
            self.downloads_table.setItem(row, 0, QTableWidgetItem(str(data['id'])))
            self.downloads_table.setItem(row, 1, QTableWidgetItem(data.get('user_login', 'N/A')))
            self.downloads_table.setItem(row, 2, QTableWidgetItem(data.get('post_title', 'N/A')))
            self.downloads_table.setItem(row, 3, QTableWidgetItem(data.get('ip', '')))
            
            create_ts = data.get('create_time')
            time_str = ''
            if create_ts:
                try:
                    time_str = datetime.fromtimestamp(int(create_ts)).strftime('%Y-%m-%d %H:%M')
                except: pass
            self.downloads_table.setItem(row, 4, QTableWidgetItem(time_str))

        self.update_download_pagination_controls()

    def update_download_pagination_controls(self):
        self.download_prev_page_btn.setEnabled(self.current_download_page > 1)
        total_pages = (self.total_downloads + self.downloads_per_page - 1) // self.downloads_per_page or 1
        self.download_next_page_btn.setEnabled(self.current_download_page < total_pages)
        self.download_page_label.setText(f"第 {self.current_download_page} / {total_pages} 页 (共 {self.total_downloads} 条)")

    def prev_download_page(self):
        if self.current_download_page > 1:
            self.current_download_page -= 1
            self.refresh_downloads()

    def next_download_page(self):
        total_pages = (self.total_downloads + self.downloads_per_page - 1) // self.downloads_per_page
        if self.current_download_page < total_pages:
            self.current_download_page += 1
            self.refresh_downloads()

    def refresh_zixuan_files(self):
        """刷新自选网站更新文件列表"""
        try:
            self.file_status_label.setText("正在读取自选网站更新文件...")
            # 在开发环境中使用测试路径，在生产环境中使用实际路径
            zixuan_path = "/www/wwwroot/zixuan"
            if not os.path.exists(zixuan_path):
                # 如果生产路径不存在，使用测试路径
                zixuan_path = "test_zixuan"

            if not os.path.exists(zixuan_path):
                self.zixuan_files_display.setText(f"错误：路径 {zixuan_path} 不存在")
                self.file_status_label.setText("路径不存在")
                return

            files_info = []
            files_info.append(f"📁 自选网站更新文件列表")
            files_info.append(f"📍 路径: {zixuan_path}")
            files_info.append("=" * 50)

            # 获取文件列表
            try:
                items = os.listdir(zixuan_path)
                items.sort()

                file_count = 0
                dir_count = 0

                for item in items:
                    item_path = os.path.join(zixuan_path, item)
                    if os.path.isfile(item_path):
                        file_size = os.path.getsize(item_path)
                        file_size_str = self.format_file_size(file_size)
                        mod_time = os.path.getmtime(item_path)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                        files_info.append(f"📄 {item} ({file_size_str}) - {mod_time_str}")
                        file_count += 1
                    elif os.path.isdir(item_path):
                        files_info.append(f"📁 {item}/")
                        dir_count += 1

                files_info.append("=" * 50)
                files_info.append(f"📊 统计: {dir_count} 个文件夹, {file_count} 个文件")

            except PermissionError:
                files_info.append("❌ 权限不足，无法读取目录内容")
            except Exception as e:
                files_info.append(f"❌ 读取目录时出错: {str(e)}")

            self.zixuan_files_display.setText("\n".join(files_info))
            self.file_status_label.setText("自选网站更新文件列表已刷新")

        except Exception as e:
            self.zixuan_files_display.setText(f"读取文件列表时出错: {str(e)}")
            self.file_status_label.setText("读取失败")

    def refresh_submissions_files(self):
        """刷新自选游戏文件列表"""
        try:
            self.file_status_label.setText("正在读取自选游戏文件...")
            # 在开发环境中使用测试路径，在生产环境中使用实际路径
            submissions_path = "/www/wwwroot/zixuan/submissions"
            if not os.path.exists(submissions_path):
                # 如果生产路径不存在，使用测试路径
                submissions_path = "test_zixuan/submissions"

            if not os.path.exists(submissions_path):
                self.submissions_files_display.setText(f"错误：路径 {submissions_path} 不存在")
                self.file_status_label.setText("路径不存在")
                return

            files_info = []
            files_info.append(f"🎮 自选游戏文件列表")
            files_info.append(f"📍 路径: {submissions_path}")
            files_info.append("=" * 50)

            # 获取文件列表
            try:
                items = os.listdir(submissions_path)
                items.sort()

                file_count = 0
                dir_count = 0

                for item in items:
                    item_path = os.path.join(submissions_path, item)
                    if os.path.isfile(item_path):
                        file_size = os.path.getsize(item_path)
                        file_size_str = self.format_file_size(file_size)
                        mod_time = os.path.getmtime(item_path)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                        files_info.append(f"🎮 {item} ({file_size_str}) - {mod_time_str}")
                        file_count += 1
                    elif os.path.isdir(item_path):
                        files_info.append(f"📁 {item}/")
                        dir_count += 1

                files_info.append("=" * 50)
                files_info.append(f"📊 统计: {dir_count} 个文件夹, {file_count} 个文件")

            except PermissionError:
                files_info.append("❌ 权限不足，无法读取目录内容")
            except Exception as e:
                files_info.append(f"❌ 读取目录时出错: {str(e)}")

            self.submissions_files_display.setText("\n".join(files_info))
            self.file_status_label.setText("自选游戏文件列表已刷新")

        except Exception as e:
            self.submissions_files_display.setText(f"读取文件列表时出错: {str(e)}")
            self.file_status_label.setText("读取失败")

    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()