#!/usr/bin/env python3
"""
测试删除功能和上传覆盖修复
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QMessageBox, QTextEdit)
from PyQt6.QtCore import Qt

class DeleteAndUploadTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("删除功能和上传覆盖测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("删除功能和上传覆盖修复测试")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 新功能说明
        new_features = """
🆕 新增功能：

1. 删除文件功能 🗑️
   ✅ 添加了红色的"删除选中文件"按钮
   ✅ 选择文件后按钮自动启用
   ✅ 删除前有确认对话框
   ✅ 安全提示：此操作不可恢复
   ✅ 删除后自动刷新文件列表

2. 上传覆盖修复 🔄
   ✅ 检测服务器上是否存在同名文件
   ✅ 如果文件存在，显示覆盖确认对话框
   ✅ 明确提示将替换现有文件
   ✅ 防止上传卡住的问题
   ✅ 用户可以选择是否覆盖
        """
        
        features_label = QLabel(new_features)
        features_label.setStyleSheet("background: #e8f5e8; padding: 15px; border: 1px solid #4caf50; border-radius: 5px; font-family: monospace; font-size: 12px;")
        layout.addWidget(features_label)
        
        # 测试步骤
        test_steps = """
📋 测试步骤：

删除功能测试：
1. 启动主程序，连接SSH服务器
2. 刷新文件列表，选择一个文件
3. 观察红色的"删除选中文件"按钮变为可用
4. 点击删除按钮
5. 确认删除对话框出现，包含安全警告
6. 选择"是"确认删除
7. 文件被删除，列表自动刷新

上传覆盖测试：
1. 选择一个本地文件上传到服务器
2. 上传完成后，再次上传同一个文件
3. 应该出现"文件已存在"对话框
4. 提示是否要覆盖现有文件
5. 选择"是"进行覆盖，或"否"取消
6. 不会再出现卡住的问题

按钮状态：
• 未连接SSH：所有操作按钮禁用
• 已连接但未选择文件：上传和刷新启用，下载和删除禁用
• 已连接且选择文件：所有按钮启用（删除按钮为红色）
        """
        
        steps_label = QLabel(test_steps)
        steps_label.setStyleSheet("background: #f0f8ff; padding: 15px; border: 1px solid #2196f3; border-radius: 5px; font-family: monospace; font-size: 12px;")
        layout.addWidget(steps_label)
        
        # 安全提示
        safety_warning = """
⚠️ 安全提示：

删除功能：
• 删除操作不可恢复，请谨慎使用
• 只能删除文件，不能删除文件夹
• 删除前会有确认对话框
• 建议在删除重要文件前先备份

上传覆盖：
• 覆盖会完全替换服务器上的现有文件
• 覆盖前会有明确的警告提示
• 可以选择取消覆盖操作
• 建议重要文件覆盖前先备份
        """
        
        warning_label = QLabel(safety_warning)
        warning_label.setStyleSheet("background: #fff3cd; padding: 15px; border: 1px solid #ffc107; border-radius: 5px; font-family: monospace; font-size: 12px;")
        layout.addWidget(warning_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        start_btn = QPushButton("启动主程序")
        start_btn.clicked.connect(self.start_main_app)
        start_btn.setStyleSheet("font-size: 16px; padding: 10px 20px; background: #4caf50; color: white;")
        button_layout.addWidget(start_btn)
        
        demo_btn = QPushButton("查看功能演示")
        demo_btn.clicked.connect(self.show_demo)
        demo_btn.setStyleSheet("font-size: 16px; padding: 10px 20px;")
        button_layout.addWidget(demo_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("准备测试删除功能和上传覆盖修复")
        self.status_label.setStyleSheet("background: #f0f0f0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setPlaceholderText("测试日志...")
        layout.addWidget(self.log_text)
        
        self.main_window = None
    
    def log(self, message):
        """添加日志信息"""
        self.log_text.append(f"[LOG] {message}")
        print(f"DEBUG: {message}")
    
    def start_main_app(self):
        """启动主程序"""
        try:
            self.log("正在启动包含删除功能的主程序...")
            
            from app import MainWindow
            self.main_window = MainWindow()
            
            self.log("主程序创建成功")
            
            # 切换到文件管理页面
            self.main_window.switch_page("file_management")
            self.log("已切换到文件管理页面")
            
            # 显示主程序
            self.main_window.show()
            self.log("主程序窗口已显示")
            
            # 检查新功能
            self.check_new_features()
            
            self.status_label.setText("✅ 主程序已启动，请测试新功能")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px; font-weight: bold;")
            
            QMessageBox.information(self, "启动成功", 
                                   "主程序已启动！\n\n"
                                   "新增功能：\n"
                                   "• 🗑️ 删除选中文件按钮（红色）\n"
                                   "• 🔄 上传覆盖确认功能\n"
                                   "• ⚠️ 安全确认对话框\n"
                                   "• 🔄 自动刷新文件列表\n\n"
                                   "请按照测试步骤验证新功能。")
            
        except Exception as e:
            error_msg = f"主程序启动失败: {str(e)}"
            self.log(error_msg)
            self.status_label.setText(f"❌ {error_msg}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px; font-weight: bold;")
            QMessageBox.warning(self, "错误", error_msg)
    
    def check_new_features(self):
        """检查新功能"""
        if not self.main_window:
            return
        
        try:
            # 检查删除按钮
            if hasattr(self.main_window, 'delete_btn'):
                self.log("✅ 删除按钮已添加")
                delete_enabled = self.main_window.delete_btn.isEnabled()
                self.log(f"删除按钮初始状态: {'启用' if delete_enabled else '禁用'} (应该是禁用)")
                
                # 检查按钮样式
                style = self.main_window.delete_btn.styleSheet()
                if "dc3545" in style or "red" in style.lower():
                    self.log("✅ 删除按钮样式正确（红色）")
                else:
                    self.log("⚠️ 删除按钮样式可能不正确")
            else:
                self.log("❌ 删除按钮未找到")
            
            # 检查按钮信号连接
            if hasattr(self.main_window, 'delete_btn'):
                delete_btn = self.main_window.delete_btn
                receivers = delete_btn.receivers(delete_btn.clicked)
                self.log(f"删除按钮信号连接数: {receivers}")
            
            # 检查上传方法是否包含覆盖检查
            if hasattr(self.main_window, 'upload_file'):
                self.log("✅ 上传方法存在，应该包含覆盖检查")
            
        except Exception as e:
            self.log(f"检查新功能时出错: {str(e)}")
    
    def show_demo(self):
        """显示功能演示"""
        demo_info = """
🎬 功能演示说明

删除功能演示：
1. 连接SSH服务器
2. 刷新文件列表
3. 点击选择一个文件
4. 观察红色删除按钮变为可用
5. 点击删除按钮
6. 查看确认对话框：
   "确定要删除服务器上的文件吗？
    文件: example.txt
    路径: /www/wwwroot/zixuan/example.txt
    ⚠️ 此操作不可恢复！"
7. 选择确认或取消

上传覆盖演示：
1. 选择一个文件上传
2. 上传完成后，再次上传同一文件
3. 查看覆盖确认对话框：
   "服务器上已存在同名文件，是否要覆盖？
    本地文件: /path/to/file.txt
    远程路径: /www/wwwroot/zixuan/file.txt
    ⚠️ 覆盖将替换服务器上的现有文件！"
4. 选择覆盖或取消

安全特性：
• 所有危险操作都有确认对话框
• 明确的警告信息
• 默认选择为"否"（安全选项）
• 详细的文件路径显示
        """
        
        QMessageBox.information(self, "功能演示", demo_info)

def main():
    app = QApplication(sys.argv)
    window = DeleteAndUploadTest()
    window.show()
    
    print("DEBUG: 删除功能和上传覆盖测试程序已启动")
    print("DEBUG: 新功能已添加并可以测试")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
