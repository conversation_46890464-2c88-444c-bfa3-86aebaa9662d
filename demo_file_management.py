#!/usr/bin/env python3
"""
演示文件管理功能的脚本
"""

import sys
import os
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel, QGroupBox
from PyQt6.QtCore import Qt

class FileManagementDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件管理功能演示")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("文件管理功能演示")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 创建两个主要区域
        main_content_layout = QHBoxLayout()
        
        # 左侧：自选网站更新
        zixuan_group = QGroupBox("自选网站更新")
        zixuan_layout = QVBoxLayout(zixuan_group)
        
        zixuan_info_label = QLabel("路径: /www/wwwroot/zixuan (测试: test_zixuan)")
        zixuan_info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        zixuan_layout.addWidget(zixuan_info_label)
        
        zixuan_refresh_btn = QPushButton("刷新文件列表")
        zixuan_refresh_btn.clicked.connect(self.refresh_zixuan_files)
        zixuan_layout.addWidget(zixuan_refresh_btn)
        
        self.zixuan_files_display = QTextEdit()
        self.zixuan_files_display.setReadOnly(True)
        self.zixuan_files_display.setPlaceholderText("点击刷新按钮加载文件列表...")
        zixuan_layout.addWidget(self.zixuan_files_display)
        
        main_content_layout.addWidget(zixuan_group)
        
        # 右侧：自选游戏
        submissions_group = QGroupBox("自选游戏")
        submissions_layout = QVBoxLayout(submissions_group)
        
        submissions_info_label = QLabel("路径: /www/wwwroot/zixuan/submissions (测试: test_zixuan/submissions)")
        submissions_info_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        submissions_layout.addWidget(submissions_info_label)
        
        submissions_refresh_btn = QPushButton("刷新文件列表")
        submissions_refresh_btn.clicked.connect(self.refresh_submissions_files)
        submissions_layout.addWidget(submissions_refresh_btn)
        
        self.submissions_files_display = QTextEdit()
        self.submissions_files_display.setReadOnly(True)
        self.submissions_files_display.setPlaceholderText("点击刷新按钮加载文件列表...")
        submissions_layout.addWidget(self.submissions_files_display)
        
        main_content_layout.addWidget(submissions_group)
        
        layout.addLayout(main_content_layout)
        
        # 底部状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        layout.addWidget(self.status_label)
    
    def refresh_zixuan_files(self):
        """刷新自选网站更新文件列表"""
        try:
            self.status_label.setText("正在读取自选网站更新文件...")
            
            # 尝试生产路径，如果不存在则使用测试路径
            zixuan_path = "/www/wwwroot/zixuan"
            if not os.path.exists(zixuan_path):
                zixuan_path = "test_zixuan"
            
            if not os.path.exists(zixuan_path):
                self.zixuan_files_display.setText(f"错误：路径 {zixuan_path} 不存在")
                self.status_label.setText("路径不存在")
                return
            
            files_info = []
            files_info.append(f"📁 自选网站更新文件列表")
            files_info.append(f"📍 路径: {zixuan_path}")
            files_info.append("=" * 50)
            
            # 获取文件列表
            try:
                items = os.listdir(zixuan_path)
                items.sort()
                
                file_count = 0
                dir_count = 0
                
                for item in items:
                    item_path = os.path.join(zixuan_path, item)
                    if os.path.isfile(item_path):
                        file_size = os.path.getsize(item_path)
                        file_size_str = self.format_file_size(file_size)
                        mod_time = os.path.getmtime(item_path)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                        files_info.append(f"📄 {item} ({file_size_str}) - {mod_time_str}")
                        file_count += 1
                    elif os.path.isdir(item_path):
                        files_info.append(f"📁 {item}/")
                        dir_count += 1
                
                files_info.append("=" * 50)
                files_info.append(f"📊 统计: {dir_count} 个文件夹, {file_count} 个文件")
                
            except PermissionError:
                files_info.append("❌ 权限不足，无法读取目录内容")
            except Exception as e:
                files_info.append(f"❌ 读取目录时出错: {str(e)}")
            
            self.zixuan_files_display.setText("\n".join(files_info))
            self.status_label.setText("自选网站更新文件列表已刷新")
            
        except Exception as e:
            self.zixuan_files_display.setText(f"读取文件列表时出错: {str(e)}")
            self.status_label.setText("读取失败")
    
    def refresh_submissions_files(self):
        """刷新自选游戏文件列表"""
        try:
            self.status_label.setText("正在读取自选游戏文件...")
            
            # 尝试生产路径，如果不存在则使用测试路径
            submissions_path = "/www/wwwroot/zixuan/submissions"
            if not os.path.exists(submissions_path):
                submissions_path = "test_zixuan/submissions"
            
            if not os.path.exists(submissions_path):
                self.submissions_files_display.setText(f"错误：路径 {submissions_path} 不存在")
                self.status_label.setText("路径不存在")
                return
            
            files_info = []
            files_info.append(f"🎮 自选游戏文件列表")
            files_info.append(f"📍 路径: {submissions_path}")
            files_info.append("=" * 50)
            
            # 获取文件列表
            try:
                items = os.listdir(submissions_path)
                items.sort()
                
                file_count = 0
                dir_count = 0
                
                for item in items:
                    item_path = os.path.join(submissions_path, item)
                    if os.path.isfile(item_path):
                        file_size = os.path.getsize(item_path)
                        file_size_str = self.format_file_size(file_size)
                        mod_time = os.path.getmtime(item_path)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
                        files_info.append(f"🎮 {item} ({file_size_str}) - {mod_time_str}")
                        file_count += 1
                    elif os.path.isdir(item_path):
                        files_info.append(f"📁 {item}/")
                        dir_count += 1
                
                files_info.append("=" * 50)
                files_info.append(f"📊 统计: {dir_count} 个文件夹, {file_count} 个文件")
                
            except PermissionError:
                files_info.append("❌ 权限不足，无法读取目录内容")
            except Exception as e:
                files_info.append(f"❌ 读取目录时出错: {str(e)}")
            
            self.submissions_files_display.setText("\n".join(files_info))
            self.status_label.setText("自选游戏文件列表已刷新")
            
        except Exception as e:
            self.submissions_files_display.setText(f"读取文件列表时出错: {str(e)}")
            self.status_label.setText("读取失败")
    
    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

def main():
    app = QApplication(sys.argv)
    window = FileManagementDemo()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
