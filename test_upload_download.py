#!/usr/bin/env python3
"""
测试上传下载功能
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QMessageBox, QFileDialog)
from PyQt6.QtCore import Qt

# 导入主程序
try:
    from app import MainWindow
    MAIN_APP_AVAILABLE = True
except ImportError as e:
    MAIN_APP_AVAILABLE = False
    import_error = str(e)

class UploadDownloadTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("上传下载功能测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        title = QLabel("上传下载功能测试")
        title.setStyleSheet("font-size: 20px; font-weight: bold; margin: 20px; text-align: center;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        if not MAIN_APP_AVAILABLE:
            error_label = QLabel(f"❌ 无法导入主程序:\n{import_error}")
            error_label.setStyleSheet("color: red; background: #ffe6e6; padding: 15px; border: 1px solid red; border-radius: 5px;")
            layout.addWidget(error_label)
            return
        
        # 状态显示
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("background: #f0f0f0; padding: 10px; border: 1px solid #ccc; border-radius: 5px;")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        button_layout = QVBoxLayout()
        
        # 测试文件选择
        test_file_btn = QPushButton("测试文件选择对话框")
        test_file_btn.clicked.connect(self.test_file_dialog)
        button_layout.addWidget(test_file_btn)
        
        # 测试主程序启动
        test_main_btn = QPushButton("启动主程序并测试文件管理")
        test_main_btn.clicked.connect(self.test_main_app)
        button_layout.addWidget(test_main_btn)
        
        # 测试上传功能
        test_upload_btn = QPushButton("测试上传功能逻辑")
        test_upload_btn.clicked.connect(self.test_upload_logic)
        button_layout.addWidget(test_upload_btn)
        
        # 测试下载功能
        test_download_btn = QPushButton("测试下载功能逻辑")
        test_download_btn.clicked.connect(self.test_download_logic)
        button_layout.addWidget(test_download_btn)
        
        layout.addLayout(button_layout)
        
        # 说明文本
        info_text = """
测试说明：
1. 点击"测试文件选择对话框"验证文件选择功能
2. 点击"启动主程序并测试文件管理"打开主程序
3. 在主程序中：
   - 先连接SSH服务器
   - 然后测试上传下载功能
4. 如果按钮无响应，请检查：
   - SSH是否已连接
   - 按钮是否已启用
   - 控制台是否有错误信息
        """
        
        info_label = QLabel(info_text)
        info_label.setStyleSheet("background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; font-family: monospace;")
        layout.addWidget(info_label)
    
    def test_file_dialog(self):
        """测试文件选择对话框"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "选择测试文件", 
                "", 
                "所有文件 (*.*)"
            )
            
            if file_path:
                self.status_label.setText(f"✅ 文件选择成功: {file_path}")
                self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            else:
                self.status_label.setText("❌ 未选择文件")
                self.status_label.setStyleSheet("color: orange; background: #fff3cd; padding: 10px; border: 1px solid orange; border-radius: 5px;")
                
        except Exception as e:
            self.status_label.setText(f"❌ 文件选择失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")
    
    def test_main_app(self):
        """测试主程序启动"""
        try:
            self.main_window = MainWindow()
            self.main_window.show()
            # 切换到文件管理页面
            self.main_window.switch_page("file_management")
            
            self.status_label.setText("✅ 主程序已启动，已切换到文件管理页面")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            
            QMessageBox.information(self, "成功", 
                                   "主程序已启动！\n\n"
                                   "请按以下步骤测试：\n"
                                   "1. 点击'连接服务器'建立SSH连接\n"
                                   "2. 连接成功后测试'上传文件'按钮\n"
                                   "3. 选择文件后测试'下载选中文件'按钮\n\n"
                                   "如果按钮无响应，请检查控制台错误信息。")
            
        except Exception as e:
            self.status_label.setText(f"❌ 主程序启动失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")
            QMessageBox.warning(self, "错误", f"主程序启动失败:\n{str(e)}")
    
    def test_upload_logic(self):
        """测试上传功能逻辑"""
        try:
            # 模拟上传逻辑测试
            file_path, _ = QFileDialog.getOpenFileName(
                self, 
                "选择要上传的测试文件", 
                "", 
                "所有文件 (*.*)"
            )
            
            if not file_path:
                self.status_label.setText("❌ 未选择文件，上传测试取消")
                return
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.status_label.setText("❌ 选择的文件不存在")
                return
            
            # 获取文件信息
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            # 模拟远程路径
            remote_path = f"/www/wwwroot/zixuan/{filename}"
            
            self.status_label.setText(f"✅ 上传逻辑测试成功\n文件: {filename}\n大小: {file_size} 字节\n目标: {remote_path}")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            
            QMessageBox.information(self, "上传逻辑测试", 
                                   f"上传逻辑测试成功！\n\n"
                                   f"本地文件: {file_path}\n"
                                   f"文件大小: {file_size} 字节\n"
                                   f"目标路径: {remote_path}\n\n"
                                   f"注意：这只是逻辑测试，实际上传需要SSH连接。")
            
        except Exception as e:
            self.status_label.setText(f"❌ 上传逻辑测试失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")
    
    def test_download_logic(self):
        """测试下载功能逻辑"""
        try:
            # 模拟下载逻辑测试
            save_path, _ = QFileDialog.getSaveFileName(
                self,
                "选择保存位置",
                "test_download.txt",
                "所有文件 (*.*)"
            )
            
            if not save_path:
                self.status_label.setText("❌ 未选择保存位置，下载测试取消")
                return
            
            # 模拟远程文件
            remote_file = "test_file.txt"
            remote_path = f"/www/wwwroot/zixuan/{remote_file}"
            
            self.status_label.setText(f"✅ 下载逻辑测试成功\n远程文件: {remote_file}\n保存到: {save_path}")
            self.status_label.setStyleSheet("color: green; background: #e6ffe6; padding: 10px; border: 1px solid green; border-radius: 5px;")
            
            QMessageBox.information(self, "下载逻辑测试", 
                                   f"下载逻辑测试成功！\n\n"
                                   f"远程文件: {remote_path}\n"
                                   f"保存位置: {save_path}\n\n"
                                   f"注意：这只是逻辑测试，实际下载需要SSH连接。")
            
        except Exception as e:
            self.status_label.setText(f"❌ 下载逻辑测试失败: {str(e)}")
            self.status_label.setStyleSheet("color: red; background: #ffe6e6; padding: 10px; border: 1px solid red; border-radius: 5px;")

def main():
    app = QApplication(sys.argv)
    window = UploadDownloadTest()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
